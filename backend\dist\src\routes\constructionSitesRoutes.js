"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const constructionSitesController_1 = require("../controllers/constructionSitesController");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(auth_1.authenticateToken);
// Construction sites routes
router.get('/', constructionSitesController_1.getConstructionSites);
router.get('/:id', constructionSitesController_1.getConstructionSiteById);
router.post('/', constructionSitesController_1.createConstructionSite);
router.put('/:id', constructionSitesController_1.updateConstructionSite);
router.delete('/:id', constructionSitesController_1.deleteConstructionSite);
// Attendance routes
router.post('/:id/attendance', constructionSitesController_1.recordAttendance);
router.get('/:id/attendance', constructionSitesController_1.getAttendance);
exports.default = router;
