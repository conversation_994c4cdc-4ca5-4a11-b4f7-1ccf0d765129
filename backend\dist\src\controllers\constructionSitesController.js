"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAttendance = exports.recordAttendance = exports.deleteConstructionSite = exports.updateConstructionSite = exports.createConstructionSite = exports.getConstructionSiteById = exports.getConstructionSites = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const prisma = new client_1.PrismaClient();
// Validation schemas
const createConstructionSiteSchema = zod_1.z.object({
    name: zod_1.z.string().min(1),
    location: zod_1.z.string().min(1),
    workers: zod_1.z.number().int().min(0),
    status: zod_1.z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'INACTIVE']),
    progress: zod_1.z.number().min(0).max(100),
    description: zod_1.z.string().optional(),
    startDate: zod_1.z.string().optional(),
    expectedEndDate: zod_1.z.string().optional(),
});
const updateConstructionSiteSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).optional(),
    location: zod_1.z.string().min(1).optional(),
    workers: zod_1.z.number().int().min(0).optional(),
    status: zod_1.z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'INACTIVE']).optional(),
    progress: zod_1.z.number().min(0).max(100).optional(),
    description: zod_1.z.string().optional(),
    startDate: zod_1.z.string().optional(),
    expectedEndDate: zod_1.z.string().optional(),
});
const attendanceSchema = zod_1.z.object({
    date: zod_1.z.string(),
    presentWorkers: zod_1.z.number().int().min(0),
    notes: zod_1.z.string().optional(),
});
// Get all construction sites
const getConstructionSites = async (req, res) => {
    try {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Build filter based on user role
        let whereClause = {};
        if (userRole !== 'SUPER_ADMIN') {
            // Get user's accessible properties (construction sites are property-specific)
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            if (userProperties.length === 0) {
                return res.json({
                    success: true,
                    data: []
                });
            }
            whereClause = {
                propertyId: { in: userProperties.map(up => up.propertyId) }
            };
        }
        const constructionSites = await prisma.constructionSite.findMany({
            where: whereClause,
            include: {
                property: {
                    select: { id: true, name: true }
                },
                attendance: {
                    orderBy: { date: 'desc' },
                    take: 1 // Get latest attendance record
                },
                _count: {
                    select: {
                        attendance: true
                    }
                }
            },
            orderBy: { name: 'asc' }
        });
        // Calculate attendance rates and format response
        const formattedSites = constructionSites.map(site => {
            const latestAttendance = site.attendance[0];
            const presentToday = latestAttendance?.presentWorkers || 0;
            const attendanceRate = site.workers > 0 ? (presentToday / site.workers) * 100 : 0;
            return {
                id: site.id,
                name: site.name,
                location: site.location,
                workers: site.workers,
                presentToday,
                attendanceRate: Math.round(attendanceRate),
                status: site.status.toLowerCase(),
                progress: site.progress,
                description: site.description,
                startDate: site.startDate,
                expectedEndDate: site.expectedEndDate,
                property: site.property,
                totalAttendanceRecords: site._count.attendance,
                createdAt: site.createdAt,
                updatedAt: site.updatedAt
            };
        });
        res.json({
            success: true,
            data: formattedSites
        });
    }
    catch (error) {
        console.error('Error fetching construction sites:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch construction sites'
        });
    }
};
exports.getConstructionSites = getConstructionSites;
// Get construction site by ID
const getConstructionSiteById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const constructionSite = await prisma.constructionSite.findUnique({
            where: { id },
            include: {
                property: {
                    select: { id: true, name: true }
                },
                attendance: {
                    orderBy: { date: 'desc' },
                    take: 30 // Get last 30 days of attendance
                }
            }
        });
        if (!constructionSite) {
            return res.status(404).json({
                success: false,
                message: 'Construction site not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: constructionSite.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this construction site'
                });
            }
        }
        // Calculate attendance statistics
        const latestAttendance = constructionSite.attendance[0];
        const presentToday = latestAttendance?.presentWorkers || 0;
        const attendanceRate = constructionSite.workers > 0 ? (presentToday / constructionSite.workers) * 100 : 0;
        const formattedSite = {
            ...constructionSite,
            presentToday,
            attendanceRate: Math.round(attendanceRate),
            status: constructionSite.status.toLowerCase()
        };
        res.json({
            success: true,
            data: formattedSite
        });
    }
    catch (error) {
        console.error('Error fetching construction site:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch construction site'
        });
    }
};
exports.getConstructionSiteById = getConstructionSiteById;
// Create construction site
const createConstructionSite = async (req, res) => {
    try {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const validatedData = createConstructionSiteSchema.parse(req.body);
        const { propertyId } = req.body;
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to create construction site for this property'
                });
            }
        }
        // Verify property exists
        const property = await prisma.property.findUnique({
            where: { id: propertyId }
        });
        if (!property) {
            return res.status(404).json({
                success: false,
                message: 'Property not found'
            });
        }
        const constructionSite = await prisma.constructionSite.create({
            data: {
                ...validatedData,
                propertyId,
                status: validatedData.status.toUpperCase(),
                startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
                expectedEndDate: validatedData.expectedEndDate ? new Date(validatedData.expectedEndDate) : null,
                createdBy: userId
            },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            }
        });
        res.status(201).json({
            success: true,
            data: constructionSite,
            message: 'Construction site created successfully'
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: error.errors
            });
        }
        console.error('Error creating construction site:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create construction site'
        });
    }
};
exports.createConstructionSite = createConstructionSite;
// Update construction site
const updateConstructionSite = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const validatedData = updateConstructionSiteSchema.parse(req.body);
        // Check if construction site exists
        const existingSite = await prisma.constructionSite.findUnique({
            where: { id }
        });
        if (!existingSite) {
            return res.status(404).json({
                success: false,
                message: 'Construction site not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: existingSite.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to update this construction site'
                });
            }
        }
        const updateData = { ...validatedData };
        if (validatedData.status) {
            updateData.status = validatedData.status.toUpperCase();
        }
        if (validatedData.startDate) {
            updateData.startDate = new Date(validatedData.startDate);
        }
        if (validatedData.expectedEndDate) {
            updateData.expectedEndDate = new Date(validatedData.expectedEndDate);
        }
        const updatedSite = await prisma.constructionSite.update({
            where: { id },
            data: updateData,
            include: {
                property: {
                    select: { id: true, name: true }
                }
            }
        });
        res.json({
            success: true,
            data: updatedSite,
            message: 'Construction site updated successfully'
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: error.errors
            });
        }
        console.error('Error updating construction site:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update construction site'
        });
    }
};
exports.updateConstructionSite = updateConstructionSite;
// Delete construction site
const deleteConstructionSite = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Check if construction site exists
        const existingSite = await prisma.constructionSite.findUnique({
            where: { id },
            include: {
                attendance: true
            }
        });
        if (!existingSite) {
            return res.status(404).json({
                success: false,
                message: 'Construction site not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: existingSite.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to delete this construction site'
                });
            }
        }
        // Delete attendance records first
        await prisma.constructionSiteAttendance.deleteMany({
            where: { constructionSiteId: id }
        });
        // Delete construction site
        await prisma.constructionSite.delete({
            where: { id }
        });
        res.json({
            success: true,
            message: 'Construction site deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting construction site:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete construction site'
        });
    }
};
exports.deleteConstructionSite = deleteConstructionSite;
// Record attendance for construction site
const recordAttendance = async (req, res) => {
    try {
        const { id } = req.params; // construction site id
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const validatedData = attendanceSchema.parse(req.body);
        // Check if construction site exists
        const constructionSite = await prisma.constructionSite.findUnique({
            where: { id }
        });
        if (!constructionSite) {
            return res.status(404).json({
                success: false,
                message: 'Construction site not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: constructionSite.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to record attendance for this construction site'
                });
            }
        }
        // Check if attendance already exists for this date
        const existingAttendance = await prisma.constructionSiteAttendance.findUnique({
            where: {
                constructionSiteId_date: {
                    constructionSiteId: id,
                    date: new Date(validatedData.date)
                }
            }
        });
        let attendance;
        if (existingAttendance) {
            // Update existing attendance
            attendance = await prisma.constructionSiteAttendance.update({
                where: { id: existingAttendance.id },
                data: {
                    presentWorkers: validatedData.presentWorkers,
                    notes: validatedData.notes,
                    updatedAt: new Date()
                }
            });
        }
        else {
            // Create new attendance record
            attendance = await prisma.constructionSiteAttendance.create({
                data: {
                    constructionSiteId: id,
                    date: new Date(validatedData.date),
                    presentWorkers: validatedData.presentWorkers,
                    notes: validatedData.notes,
                    recordedBy: userId
                }
            });
        }
        res.json({
            success: true,
            data: attendance,
            message: existingAttendance ? 'Attendance updated successfully' : 'Attendance recorded successfully'
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: error.errors
            });
        }
        console.error('Error recording attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to record attendance'
        });
    }
};
exports.recordAttendance = recordAttendance;
// Get attendance for construction site
const getAttendance = async (req, res) => {
    try {
        const { id } = req.params; // construction site id
        const { startDate, endDate, page = '1', limit = '30' } = req.query;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Check if construction site exists
        const constructionSite = await prisma.constructionSite.findUnique({
            where: { id }
        });
        if (!constructionSite) {
            return res.status(404).json({
                success: false,
                message: 'Construction site not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: constructionSite.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to view attendance for this construction site'
                });
            }
        }
        // Build date filter
        const dateFilter = { constructionSiteId: id };
        if (startDate || endDate) {
            dateFilter.date = {};
            if (startDate)
                dateFilter.date.gte = new Date(startDate);
            if (endDate)
                dateFilter.date.lte = new Date(endDate);
        }
        // Get total count
        const total = await prisma.constructionSiteAttendance.count({
            where: dateFilter
        });
        // Get attendance records
        const attendanceRecords = await prisma.constructionSiteAttendance.findMany({
            where: dateFilter,
            orderBy: { date: 'desc' },
            skip,
            take: limitNum,
            include: {
                recordedByUser: {
                    select: { id: true, name: true, email: true }
                }
            }
        });
        const totalPages = Math.ceil(total / limitNum);
        res.json({
            success: true,
            data: attendanceRecords,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                totalPages
            }
        });
    }
    catch (error) {
        console.error('Error fetching attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch attendance'
        });
    }
};
exports.getAttendance = getAttendance;
