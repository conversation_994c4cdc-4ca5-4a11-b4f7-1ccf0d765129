"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSecurityAnalytics = exports.getMaintenanceAnalytics = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const prisma = new client_1.PrismaClient();
// Validation schemas
const analyticsQuerySchema = zod_1.z.object({
    propertyId: zod_1.z.string().optional(),
    startDate: zod_1.z.string().optional(),
    endDate: zod_1.z.string().optional(),
    timeRange: zod_1.z.enum(['24h', '7d', '30d', '90d']).optional(),
});
// Get maintenance analytics
const getMaintenanceAnalytics = async (req, res) => {
    try {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const query = analyticsQuerySchema.parse(req.query);
        // Calculate date range
        const endDate = query.endDate ? new Date(query.endDate) : new Date();
        let startDate;
        switch (query.timeRange) {
            case '24h':
                startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = query.startDate ? new Date(query.startDate) : new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
        // Build property filter
        let propertyFilter = {};
        if (query.propertyId) {
            propertyFilter = { propertyId: query.propertyId };
        }
        else if (userRole !== 'SUPER_ADMIN') {
            // Get user's accessible properties
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            if (userProperties.length === 0) {
                return res.json({
                    success: true,
                    data: {
                        overview: { totalIssues: 0, resolvedIssues: 0, openIssues: 0, inProgressIssues: 0 },
                        trends: [],
                        departments: [],
                        performance: { avgResolutionTime: 0, resolutionRate: 0, customerSatisfaction: 0, escalationRate: 0 }
                    }
                });
            }
            propertyFilter = {
                propertyId: { in: userProperties.map(up => up.propertyId) }
            };
        }
        // Get maintenance issues for the period
        const issues = await prisma.maintenanceIssue.findMany({
            where: {
                ...propertyFilter,
                createdAt: {
                    gte: startDate,
                    lte: endDate
                }
            },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            }
        });
        // Calculate overview metrics
        const totalIssues = issues.length;
        const resolvedIssues = issues.filter(issue => issue.status === 'RESOLVED').length;
        const openIssues = issues.filter(issue => issue.status === 'OPEN').length;
        const inProgressIssues = issues.filter(issue => issue.status === 'IN_PROGRESS').length;
        // Calculate resolution times
        const resolvedIssuesWithTime = issues.filter(issue => issue.status === 'RESOLVED' && issue.resolvedAt);
        const avgResolutionTime = resolvedIssuesWithTime.length > 0
            ? resolvedIssuesWithTime.reduce((sum, issue) => {
                const resolutionTime = issue.resolvedAt.getTime() - issue.createdAt.getTime();
                return sum + (resolutionTime / (1000 * 60 * 60 * 24)); // Convert to days
            }, 0) / resolvedIssuesWithTime.length
            : 0;
        // Calculate trends (daily counts for the period)
        const trends = [];
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const dayStart = new Date(currentDate);
            const dayEnd = new Date(currentDate);
            dayEnd.setHours(23, 59, 59, 999);
            const dayIssues = issues.filter(issue => issue.createdAt >= dayStart && issue.createdAt <= dayEnd);
            trends.push({
                date: currentDate.toISOString().split('T')[0],
                totalIssues: dayIssues.length,
                resolvedIssues: dayIssues.filter(issue => issue.status === 'RESOLVED').length,
                openIssues: dayIssues.filter(issue => issue.status === 'OPEN').length
            });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        // Calculate department statistics
        const departmentStats = issues.reduce((acc, issue) => {
            const dept = issue.departmentName || 'Unknown';
            if (!acc[dept]) {
                acc[dept] = { total: 0, resolved: 0, open: 0, inProgress: 0 };
            }
            acc[dept].total++;
            if (issue.status === 'RESOLVED')
                acc[dept].resolved++;
            else if (issue.status === 'OPEN')
                acc[dept].open++;
            else if (issue.status === 'IN_PROGRESS')
                acc[dept].inProgress++;
            return acc;
        }, {});
        const departments = Object.entries(departmentStats).map(([name, stats]) => ({
            name,
            ...stats,
            resolutionRate: stats.total > 0 ? (stats.resolved / stats.total) * 100 : 0
        }));
        // Calculate performance metrics
        const resolutionRate = totalIssues > 0 ? (resolvedIssues / totalIssues) * 100 : 0;
        const escalationRate = totalIssues > 0 ? (issues.filter(issue => issue.priority === 'HIGH' || issue.priority === 'URGENT' || issue.priority === 'CRITICAL').length / totalIssues) * 100 : 0;
        // Mock customer satisfaction (in real implementation, this would come from feedback)
        const customerSatisfaction = 4.2;
        const analytics = {
            overview: {
                totalIssues,
                resolvedIssues,
                openIssues,
                inProgressIssues,
                resolutionRate: Math.round(resolutionRate * 10) / 10,
                avgResolutionTime: Math.round(avgResolutionTime * 10) / 10
            },
            trends,
            departments,
            performance: {
                avgResolutionTime: Math.round(avgResolutionTime * 10) / 10,
                resolutionRate: Math.round(resolutionRate * 10) / 10,
                customerSatisfaction,
                escalationRate: Math.round(escalationRate * 10) / 10
            },
            timeRange: {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                period: query.timeRange || 'custom'
            }
        };
        res.json({
            success: true,
            data: analytics
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: error.errors
            });
        }
        console.error('Error fetching maintenance analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch maintenance analytics'
        });
    }
};
exports.getMaintenanceAnalytics = getMaintenanceAnalytics;
// Get security analytics
const getSecurityAnalytics = async (req, res) => {
    try {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const query = analyticsQuerySchema.parse(req.query);
        // Calculate date range
        const endDate = query.endDate ? new Date(query.endDate) : new Date();
        let startDate;
        switch (query.timeRange) {
            case '24h':
                startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = query.startDate ? new Date(query.startDate) : new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
        // Build property filter
        let propertyFilter = {};
        if (query.propertyId) {
            propertyFilter = { propertyId: query.propertyId };
        }
        else if (userRole !== 'SUPER_ADMIN') {
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            if (userProperties.length === 0) {
                return res.json({
                    success: true,
                    data: {
                        overview: { totalIncidents: 0, resolvedIncidents: 0, activeIncidents: 0 },
                        trends: [],
                        systemHealth: { operational: 0, warning: 0, critical: 0 }
                    }
                });
            }
            propertyFilter = {
                propertyId: { in: userProperties.map(up => up.propertyId) }
            };
        }
        // Get security incidents
        const incidents = await prisma.securityIncident.findMany({
            where: {
                ...propertyFilter,
                createdAt: {
                    gte: startDate,
                    lte: endDate
                }
            },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            }
        });
        // Get security systems status
        const securitySystems = await prisma.securitySystem.findMany({
            where: propertyFilter,
            include: {
                property: {
                    select: { id: true, name: true }
                }
            }
        });
        // Calculate overview metrics
        const totalIncidents = incidents.length;
        const resolvedIncidents = incidents.filter(incident => incident.status === 'RESOLVED').length;
        const activeIncidents = incidents.filter(incident => incident.status === 'ACTIVE').length;
        // Calculate system health
        const operationalSystems = securitySystems.filter(system => system.alarmStatus === 'ARMED' || system.alarmStatus === 'DISARMED').length;
        const warningSystems = securitySystems.filter(system => system.alarmStatus === 'MAINTENANCE').length;
        const criticalSystems = securitySystems.filter(system => system.alarmStatus === 'TRIGGERED').length;
        // Calculate trends
        const trends = [];
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const dayStart = new Date(currentDate);
            const dayEnd = new Date(currentDate);
            dayEnd.setHours(23, 59, 59, 999);
            const dayIncidents = incidents.filter(incident => incident.createdAt >= dayStart && incident.createdAt <= dayEnd);
            trends.push({
                date: currentDate.toISOString().split('T')[0],
                totalIncidents: dayIncidents.length,
                resolvedIncidents: dayIncidents.filter(incident => incident.status === 'RESOLVED').length,
                activeIncidents: dayIncidents.filter(incident => incident.status === 'ACTIVE').length
            });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        const analytics = {
            overview: {
                totalIncidents,
                resolvedIncidents,
                activeIncidents,
                resolutionRate: totalIncidents > 0 ? (resolvedIncidents / totalIncidents) * 100 : 0
            },
            trends,
            systemHealth: {
                operational: operationalSystems,
                warning: warningSystems,
                critical: criticalSystems,
                total: securitySystems.length
            },
            timeRange: {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                period: query.timeRange || 'custom'
            }
        };
        res.json({
            success: true,
            data: analytics
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: error.errors
            });
        }
        console.error('Error fetching security analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch security analytics'
        });
    }
};
exports.getSecurityAnalytics = getSecurityAnalytics;
