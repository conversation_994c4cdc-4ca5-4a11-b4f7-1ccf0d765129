import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../screens/auth/login_screen.dart';
import '../screens/main/main_navigation_screen.dart';
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/properties/properties_screen.dart';
import '../screens/properties/property_detail_screen.dart';
import '../screens/office/office_management_screen.dart';
import '../screens/office/office_detail_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/splash/splash_screen.dart';
import '../screens/systems/water_management_screen.dart';
import '../screens/systems/electricity_management_screen.dart';
import '../screens/systems/security_management_screen.dart';
import '../screens/systems/internet_management_screen.dart';
import '../screens/systems/ott_services_screen.dart';
import '../screens/maintenance/maintenance_screen.dart';
import '../screens/admin/admin_panel_screen.dart';

// Route names
class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String main = '/main';
  static const String dashboard = '/dashboard';
  static const String properties = '/properties';
  static const String propertyDetail = '/properties/:propertyId';
  static const String officeManagement = '/office';
  static const String settings = '/settings';
  static const String adminPanel = '/admin';

  // Property sub-routes
  static const String waterManagement = '/properties/:propertyId/water';
  static const String electricityManagement = '/properties/:propertyId/electricity';
  static const String securityManagement = '/properties/:propertyId/security';
  static const String internetManagement = '/properties/:propertyId/internet';
  static const String ottServices = '/properties/:propertyId/ott';
  static const String propertyMaintenance = '/properties/:propertyId/maintenance';
  
  // Office sub-routes
  static const String attendance = '/office/attendance';
  static const String employees = '/office/employees';
  static const String reports = '/office/reports';
  static const String constructionSites = '/office/construction';
}

// Router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    routes: [
      // Splash Screen
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Login Screen
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Main Navigation with Shell Route for Bottom Navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigationScreen(child: child);
        },
        routes: [
          // Dashboard
          GoRoute(
            path: AppRoutes.dashboard,
            name: 'dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),
          
          // Properties
          GoRoute(
            path: AppRoutes.properties,
            name: 'properties',
            builder: (context, state) => const PropertiesScreen(),
            routes: [
              // Property Detail
              GoRoute(
                path: '/:propertyId',
                name: 'property-detail',
                builder: (context, state) {
                  final propertyId = state.pathParameters['propertyId']!;
                  return PropertyDetailScreen(propertyId: propertyId);
                },
                routes: [
                  // Water Management
                  GoRoute(
                    path: '/water',
                    name: 'water-management',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return WaterManagementScreen(propertyId: propertyId);
                    },
                  ),
                  
                  // Electricity Management
                  GoRoute(
                    path: '/electricity',
                    name: 'electricity-management',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return ElectricityManagementScreen(propertyId: propertyId);
                    },
                  ),
                  
                  // Security Management
                  GoRoute(
                    path: '/security',
                    name: 'security-management',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return SecurityManagementScreen(propertyId: propertyId);
                    },
                  ),
                  
                  // Internet Management
                  GoRoute(
                    path: '/internet',
                    name: 'internet-management',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return InternetManagementScreen(propertyId: propertyId);
                    },
                  ),
                  
                  // OTT Services
                  GoRoute(
                    path: '/ott',
                    name: 'ott-services',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return OTTServicesScreen(propertyId: propertyId);
                    },
                  ),
                  
                  // Maintenance
                  GoRoute(
                    path: '/maintenance',
                    name: 'property_maintenance',
                    builder: (context, state) {
                      final propertyId = state.pathParameters['propertyId']!;
                      return MaintenanceScreen(propertyId: propertyId);
                    },
                  ),
                ],
              ),
            ],
          ),
          
          // Office Management
          GoRoute(
            path: AppRoutes.officeManagement,
            name: 'office-management',
            builder: (context, state) => const OfficeManagementScreen(),
            routes: [
              // Office Detail
              GoRoute(
                path: '/:officeId',
                name: 'office-detail',
                builder: (context, state) {
                  final officeId = state.pathParameters['officeId']!;
                  return OfficeDetailScreen(officeId: officeId);
                },
              ),
              // Attendance
              GoRoute(
                path: '/attendance',
                name: 'attendance',
                builder: (context, state) => const AttendanceScreen(),
              ),
              
              // Employees
              GoRoute(
                path: '/employees',
                name: 'employees',
                builder: (context, state) => const EmployeesScreen(),
              ),
              
              // Reports
              GoRoute(
                path: '/reports',
                name: 'reports',
                builder: (context, state) => const ReportsScreen(),
              ),
              
              // Construction Sites
              GoRoute(
                path: '/construction',
                name: 'construction-sites',
                builder: (context, state) => const ConstructionSitesScreen(),
              ),
            ],
          ),
          
          // Settings
          GoRoute(
            path: AppRoutes.settings,
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),

          // Admin Panel
          GoRoute(
            path: AppRoutes.adminPanel,
            name: 'admin',
            builder: (context, state) => const AdminPanelScreen(),
          ),


        ],
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found: ${state.uri}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Placeholder screens that we'll implement









// MaintenanceScreen is now imported from '../screens/maintenance/maintenance_screen.dart'

class AttendanceScreen extends StatelessWidget {
  const AttendanceScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Attendance')),
      body: const Center(child: Text('Attendance Management')),
    );
  }
}

class EmployeesScreen extends StatelessWidget {
  const EmployeesScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Employees')),
      body: const Center(child: Text('Employee Management')),
    );
  }
}

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reports')),
      body: const Center(child: Text('Reports')),
    );
  }
}

class ConstructionSitesScreen extends StatelessWidget {
  const ConstructionSitesScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Construction Sites')),
      body: const Center(child: Text('Construction Sites Management')),
    );
  }
}
