"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedBasicData() {
    console.log('🌱 Seeding basic data...');
    try {
        // Create sample properties
        console.log('Creating sample properties...');
        const properties = [
            {
                name: 'Jubilee Hills Residence',
                type: client_1.PropertyType.RESIDENTIAL,
                description: 'Premium residential complex in Jubilee Hills with modern amenities',
                address: '123 Jubilee Hills, Hyderabad, Telangana 500033',
                isActive: true,
            },
            {
                name: 'Banjara Hills Apartments',
                type: client_1.PropertyType.RESIDENTIAL,
                description: 'Luxury apartments in the heart of Banjara Hills',
                address: '456 Banjara Hills, Hyderabad, Telangana 500034',
                isActive: true,
            },
            {
                name: 'Gachibowli Tech Park',
                type: client_1.PropertyType.OFFICE,
                description: 'Modern office complex in Gachibowli IT corridor',
                address: '789 Gachibowli, Hyderabad, Telangana 500032',
                isActive: true,
            },
        ];
        for (const propertyData of properties) {
            const existingProperty = await prisma.property.findFirst({
                where: { name: propertyData.name },
            });
            if (!existingProperty) {
                await prisma.property.create({
                    data: propertyData,
                });
                console.log(`  ✅ Created property: ${propertyData.name}`);
            }
            else {
                console.log(`  ⏭️  Property already exists: ${propertyData.name}`);
            }
        }
        // Create a sample admin user
        console.log('Creating sample admin user...');
        const adminUser = {
            name: 'System Administrator',
            email: '<EMAIL>',
            phone: '+91 9876543210',
            password: '$2b$10$rQZ9QmjytWIAp8Eqt.9fKOYVXzQqvqv5rQZ9QmjytWIAp8Eqt.9fKO', // password: admin123
            role: client_1.UserRole.SUPER_ADMIN,
            isActive: true,
        };
        const existingAdmin = await prisma.user.findFirst({
            where: { email: adminUser.email },
        });
        if (!existingAdmin) {
            const createdAdmin = await prisma.user.create({
                data: adminUser,
            });
            console.log(`  ✅ Created admin user: ${adminUser.email}`);
            // Assign admin to all properties
            const allProperties = await prisma.property.findMany();
            for (const property of allProperties) {
                await prisma.userProperty.create({
                    data: {
                        userId: createdAdmin.id,
                        propertyId: property.id,
                    },
                });
                console.log(`  ✅ Assigned admin to property: ${property.name}`);
            }
        }
        else {
            console.log(`  ⏭️  Admin user already exists: ${adminUser.email}`);
        }
        console.log('✅ Basic data seeded successfully!');
    }
    catch (error) {
        console.error('❌ Error seeding basic data:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the seed function
if (require.main === module) {
    seedBasicData()
        .then(() => {
        console.log('🎉 Basic data seeding completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Basic data seeding failed:', error);
        process.exit(1);
    });
}
exports.default = seedBasicData;
