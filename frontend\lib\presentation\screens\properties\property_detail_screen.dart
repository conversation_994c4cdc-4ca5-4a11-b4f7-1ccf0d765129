import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/dashboard.dart' show SystemStatusSummary;
import '../../../data/repositories/property_repository.dart' show PropertyDetail;
import '../../providers/properties_providers.dart';
import '../main/main_navigation_screen.dart';

class PropertyDetailScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const PropertyDetailScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends ConsumerState<PropertyDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final propertyDetailAsync = ref.watch(propertyDetailProvider(widget.propertyId));

    return Scaffold(
      appBar: CustomAppBar(
        title: propertyDetailAsync.when(
          data: (property) => property?.name ?? 'Property Details',
          loading: () => 'Loading...',
          error: (_, __) => 'Property Details',
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to property settings
            },
          ),
        ],
      ),
      body: propertyDetailAsync.when(
        data: (property) {
          if (property == null) {
            return _buildPropertyNotFound();
          }
          return _buildPropertyContent(property);
        },
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildPropertyContent(PropertyDetail property) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Header
          _buildPropertyHeader(property),

          // System Navigation Grid
          _buildSystemNavigationGrid(property),

          // Quick Stats
          _buildQuickStats(property),

          // Recent Activities
          _buildRecentActivities(property),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(dynamic error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load property details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(propertyDetailProvider(widget.propertyId));
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyNotFound() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Property not found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'The property you are looking for does not exist or you do not have access to it.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.go('/properties'),
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to Properties'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyHeader(PropertyDetail property) {
    Color statusColor;
    switch (property.status.toLowerCase()) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      property.name,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      property.address,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  property.status.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          if (property.description?.isNotEmpty == true)
            Text(
              property.description!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSystemNavigationGrid(PropertyDetail property) {
    final systems = property.systemStatusSummaries;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Management',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 16),

          if (systems.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.settings_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No systems configured',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.4,
              ),
              itemCount: systems.length,
              itemBuilder: (context, index) {
                final systemData = systems[index] as Map<String, dynamic>;
                return _buildSystemCard(systemData);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildSystemCard(Map<String, dynamic> system) {
    Color statusColor;
    IconData systemIcon;
    String systemName;

    final systemType = (system['systemType'] ?? '').toString().toLowerCase();
    final status = (system['status'] ?? '').toString().toLowerCase();

    switch (systemType) {
      case 'water':
        systemIcon = Icons.water_drop;
        systemName = 'Water Management';
        break;
      case 'electricity':
        systemIcon = Icons.electrical_services;
        systemName = 'Electricity';
        break;
      case 'security':
        systemIcon = Icons.security;
        systemName = 'Security';
        break;
      case 'internet':
        systemIcon = Icons.wifi;
        systemName = 'Internet';
        break;
      case 'ott':
        systemIcon = Icons.tv;
        systemName = 'OTT Services';
        break;
      case 'maintenance':
        systemIcon = Icons.build;
        systemName = 'Maintenance';
        break;
      default:
        systemIcon = Icons.settings;
        systemName = systemType.isNotEmpty ? systemType : 'System';
    }

    switch (status) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Navigate to specific system management screen
          context.go('/properties/${widget.propertyId}/${systemType}');
        },
        child: Padding(
          padding: const EdgeInsets.all(12), // Reduced padding
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // Prevent overflow
            children: [
              // System Icon with Status
              Stack(
                children: [
                  Container(
                    width: 40, // Reduced from 48
                    height: 40, // Reduced from 48
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      systemIcon,
                      color: statusColor,
                      size: 20, // Reduced from 24
                    ),
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 10, // Reduced from 12
                      height: 10, // Reduced from 12
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1.5),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8), // Reduced from 12

              // System Name
              Flexible( // Wrap in Flexible to prevent overflow
                child: Text(
                  systemName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 12, // Reduced font size
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2, // Allow 2 lines
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              const SizedBox(height: 2), // Reduced from 4

              // Last Updated
              Flexible( // Wrap in Flexible to prevent overflow
                child: Text(
                  'Updated ${_formatDateTime(system['lastChecked'] ?? '')}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontSize: 10, // Reduced font size
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats(PropertyDetail property) {
    final systems = property.systemStatusSummaries;
    final operationalCount = systems.where((s) => (s['status'] ?? '').toString().toLowerCase() == 'operational').length;
    final warningCount = systems.where((s) => (s['status'] ?? '').toString().toLowerCase() == 'warning').length;
    final criticalCount = systems.where((s) => (s['status'] ?? '').toString().toLowerCase() == 'critical').length;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Statistics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Systems',
                  systems.length.toString(),
                  Icons.dashboard,
                  AppTheme.infoColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Operational',
                  operationalCount.toString(),
                  Icons.check_circle,
                  AppTheme.successColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Warnings',
                  warningCount.toString(),
                  Icons.warning,
                  AppTheme.warningColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Critical',
                  criticalCount.toString(),
                  Icons.error,
                  AppTheme.errorColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivities(PropertyDetail property) {
    final activities = property.recentActivities;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activities',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to full activity log
                },
                child: const Text('View All'),
              ),
            ],
          ),

          const SizedBox(height: 12),

          if (activities.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.history,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No recent activities',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...activities.map((activity) => _buildActivityCard(activity as Map<String, dynamic>)),
        ],
      ),
    );
  }

  Widget _buildActivityCard(Map<String, dynamic> activity) {
    IconData activityIcon;
    Color activityColor;

    // Determine icon and color based on action type
    final action = (activity['action'] ?? '').toString().toLowerCase();
    if (action.contains('water')) {
      activityIcon = Icons.water_drop;
      activityColor = Colors.blue;
    } else if (action.contains('electric') || action.contains('power')) {
      activityIcon = Icons.electrical_services;
      activityColor = Colors.orange;
    } else if (action.contains('security')) {
      activityIcon = Icons.security;
      activityColor = Colors.green;
    } else if (action.contains('maintenance')) {
      activityIcon = Icons.build;
      activityColor = Colors.purple;
    } else if (action.contains('alert')) {
      activityIcon = Icons.warning;
      activityColor = Colors.red;
    } else {
      activityIcon = Icons.info;
      activityColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: activityColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            activityIcon,
            color: activityColor,
            size: 20,
          ),
        ),
        title: Text(
          activity['action'] ?? 'Activity',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(activity['description'] ?? 'No description'),
            const SizedBox(height: 2),
            Text(
              _formatDateTime(activity['createdAt'] ?? ''),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colors.grey[400],
        ),
        onTap: () {
          // TODO: Navigate to activity detail
        },
      ),
    );
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return 'Recently';
    }
  }
}
