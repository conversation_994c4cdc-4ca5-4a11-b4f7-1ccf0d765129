import '../models/api_response.dart';
import '../models/property.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class PropertyRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<Property>>> getProperties({
    String? type,
    String? status,
    String? search,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiClient.get(
        ApiConstants.propertiesList,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        print('🔍 DEBUG: Raw response data: ${response.data}');

        // Handle nested data structure: response.data.data contains the properties array
        final responseData = response.data['data'];
        print('🔍 DEBUG: Response data field: $responseData');

        final List<dynamic> data = responseData is Map ? (responseData['data'] ?? []) : (response.data['data'] ?? []);
        print('🔍 DEBUG: Extracted properties array: $data (length: ${data.length})');

        final properties = data.map((json) => Property.fromJson(json)).toList();
        print('🔍 DEBUG: Parsed properties: ${properties.length} items');
        properties.forEach((prop) => print('  - ${prop.name} (${prop.id})'));

        return ApiResponse.success(data: properties);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch properties: $e');
    }
  }

  Future<ApiResponse<Property>> getProperty(String id) async {
    try {
      final endpoint = ApiConstants.propertyDetail.replaceAll('{propertyId}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final property = Property.fromJson(response.data['data']);
        return ApiResponse.success(data: property);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property: $e');
    }
  }

  Future<ApiResponse<Property>> createProperty(CreatePropertyRequest request) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.propertiesList,
        data: request.toJson(),
      );

      if (response.isSuccess) {
        // Create a simplified property object from the response
        final data = response.data['data'];
        final createdProperty = Property(
          id: data['id'],
          name: data['name'],
          type: data['type'],
          address: data['address'],
          city: '', // Default empty values for required fields not in response
          state: '',
          zipCode: '',
          country: '',
          description: data['description'],
          isActive: data['isActive'] ?? true,
          status: 'ACTIVE', // Default status
          createdAt: DateTime.parse(data['createdAt']),
          updatedAt: DateTime.parse(data['updatedAt']),
          systemStatuses: const [], // Empty list for now
        );
        return ApiResponse.success(data: createdProperty);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create property: $e');
    }
  }

  Future<ApiResponse<Property>> updateProperty(String id, Property property) async {
    try {
      final endpoint = ApiConstants.propertyDetail.replaceAll('{propertyId}', id);
      final response = await _apiClient.put(
        endpoint,
        data: property.toJson(),
      );

      if (response.isSuccess) {
        final updatedProperty = Property.fromJson(response.data['data']);
        return ApiResponse.success(data: updatedProperty);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update property: $e');
    }
  }

  Future<ApiResponse<void>> deleteProperty(String id) async {
    try {
      final endpoint = ApiConstants.propertyDetail.replaceAll('{propertyId}', id);
      final response = await _apiClient.delete(endpoint);

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to delete property: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getPropertyStatistics({
    String? type,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (type != null) queryParams['type'] = type;
      if (dateFrom != null) queryParams['dateFrom'] = dateFrom;
      if (dateTo != null) queryParams['dateTo'] = dateTo;

      final response = await _apiClient.get(
        '${ApiConstants.propertiesList}/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property statistics: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getPropertyUnits(String propertyId) async {
    try {
      final endpoint = ApiConstants.propertyUnits.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property units: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getPropertyTenants(String propertyId) async {
    try {
      final endpoint = ApiConstants.propertyTenants.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property tenants: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getPropertyDocuments(String propertyId) async {
    try {
      final endpoint = ApiConstants.propertyDocuments.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property documents: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> uploadPropertyDocument({
    required String propertyId,
    required String filePath,
    required String fileName,
    required String documentType,
  }) async {
    try {
      // For now, return a placeholder response since file upload implementation
      // would require multipart form data handling
      return ApiResponse.error(error: 'File upload not implemented yet');
    } catch (e) {
      return ApiResponse.error(error: 'Failed to upload document: $e');
    }
  }

  Future<ApiResponse<PropertyDetail>> getPropertyDetail(String id) async {
    try {
      final endpoint = ApiConstants.propertyDetail.replaceAll('{propertyId}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final data = response.data['data'] ?? response.data;

        // Create PropertyDetail with safe parsing
        final propertyDetail = PropertyDetail(
          id: data['id'] ?? '',
          name: data['name'] ?? '',
          type: data['type'] ?? '',
          address: data['address'] ?? '',
          city: data['city'] ?? '',
          state: data['state'] ?? '',
          zipCode: data['zipCode'] ?? '',
          country: data['country'] ?? '',
          description: data['description'],
          totalUnits: data['totalUnits'],
          occupiedUnits: data['occupiedUnits'],
          monthlyRent: data['monthlyRent']?.toDouble(),
          propertyValue: data['propertyValue']?.toDouble(),
          managerId: data['managerId'],
          status: data['status'] ?? 'ACTIVE',
          amenities: data['amenities'] != null ? List<String>.from(data['amenities']) : null,
          images: data['images'] != null ? List<String>.from(data['images']) : null,
          coordinates: data['coordinates'] != null ? Map<String, double>.from(data['coordinates']) : null,
          isActive: data['isActive'] ?? true,
          createdAt: data['createdAt'] != null ? DateTime.parse(data['createdAt']) : DateTime.now(),
          updatedAt: data['updatedAt'] != null ? DateTime.parse(data['updatedAt']) : DateTime.now(),
          units: List<Map<String, dynamic>>.from(data['units'] ?? []),
          tenants: List<Map<String, dynamic>>.from(data['tenants'] ?? []),
          documents: List<Map<String, dynamic>>.from(data['documents'] ?? []),
          statistics: Map<String, dynamic>.from(data['statistics'] ?? {}),
          systemStatusSummaries: List<dynamic>.from(data['systemStatuses'] ?? []),
          recentActivities: List<dynamic>.from(data['recentActivities'] ?? []),
        );

        return ApiResponse.success(data: propertyDetail);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch property detail: $e');
    }
  }
}

// Property detail model for extended property information
class PropertyDetail extends Property {
  final List<Map<String, dynamic>> units;
  final List<Map<String, dynamic>> tenants;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> statistics;
  final List<dynamic> systemStatusSummaries;
  final List<dynamic> recentActivities;

  PropertyDetail({
    required super.id,
    required super.name,
    required super.type,
    required super.address,
    required super.city,
    required super.state,
    required super.zipCode,
    required super.country,
    super.description,
    super.totalUnits,
    super.occupiedUnits,
    super.monthlyRent,
    super.propertyValue,
    super.managerId,
    required super.status,
    super.amenities,
    super.images,
    super.coordinates,
    required super.isActive,
    required super.createdAt,
    required super.updatedAt,
    required this.units,
    required this.tenants,
    required this.documents,
    required this.statistics,
    required this.systemStatusSummaries,
    required this.recentActivities,
  }) : super(systemStatuses: const [], settings: null);

  factory PropertyDetail.fromJson(Map<String, dynamic> json) {
    final property = Property.fromJson(json);
    return PropertyDetail(
      id: property.id,
      name: property.name,
      type: property.type,
      address: property.address,
      city: property.city,
      state: property.state,
      zipCode: property.zipCode,
      country: property.country,
      description: property.description,
      totalUnits: property.totalUnits,
      occupiedUnits: property.occupiedUnits,
      monthlyRent: property.monthlyRent,
      propertyValue: property.propertyValue,
      managerId: property.managerId,
      status: property.status,
      amenities: property.amenities,
      images: property.images,
      coordinates: property.coordinates,
      isActive: property.isActive,
      createdAt: property.createdAt,
      updatedAt: property.updatedAt,
      units: List<Map<String, dynamic>>.from(json['units'] ?? []),
      tenants: List<Map<String, dynamic>>.from(json['tenants'] ?? []),
      documents: List<Map<String, dynamic>>.from(json['documents'] ?? []),
      statistics: Map<String, dynamic>.from(json['statistics'] ?? {}),
      systemStatusSummaries: List<dynamic>.from(json['systemStatuses'] ?? []),
      recentActivities: List<dynamic>.from(json['recentActivities'] ?? []),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'units': units,
      'tenants': tenants,
      'documents': documents,
      'statistics': statistics,
      'systemStatuses': systemStatusSummaries,
      'recentActivities': recentActivities,
    });
    return json;
  }
}

// Create Property Request Model
class CreatePropertyRequest {
  final String name;
  final String type;
  final String address;
  final String? description;

  const CreatePropertyRequest({
    required this.name,
    required this.type,
    required this.address,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'address': address,
      if (description != null) 'description': description,
    };
  }
}
