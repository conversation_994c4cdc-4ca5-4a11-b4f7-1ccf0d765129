import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/property_system_providers.dart';
import '../../providers/properties_providers.dart';
import '../../../data/models/system.dart';
import '../../../data/models/property.dart';
import 'enhanced_system_content_management_screen.dart';
import '../../../core/services/service_locator.dart';
import '../../../data/repositories/property_system_repository.dart';
import '../../../core/utils/api_response.dart';

class PropertySystemManagementScreen extends ConsumerStatefulWidget {
  const PropertySystemManagementScreen({super.key});

  @override
  ConsumerState<PropertySystemManagementScreen> createState() => _PropertySystemManagementScreenState();
}

class _PropertySystemManagementScreenState extends ConsumerState<PropertySystemManagementScreen> {
  String? selectedPropertyId;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Property System Management'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildPropertySelector(),
          if (selectedPropertyId != null) 
            Expanded(child: _buildSystemConfigList()),
        ],
      ),
    );
  }

  Widget _buildPropertySelector() {
    final propertiesAsyncValue = ref.watch(filteredPropertiesProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Property',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          propertiesAsyncValue.when(
            data: (properties) => DropdownButtonFormField<String>(
              value: selectedPropertyId,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Choose a property',
              ),
              items: properties.map((property) => DropdownMenuItem(
                value: property.id,
                child: Text(property.name),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  selectedPropertyId = value;
                });
              },
            ),
            loading: () => const CircularProgressIndicator(),
            error: (error, stack) => Text('Error loading properties: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemConfigList() {
    final configsAsyncValue = ref.watch(propertySystemConfigsProvider(selectedPropertyId!));
    
    return configsAsyncValue.when(
      data: (configs) => ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: configs.length,
        itemBuilder: (context, index) {
          final config = configs[index];
          return _buildSystemConfigCard(config);
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Error: ${error.toString()}'),
            ElevatedButton(
              onPressed: () => ref.refresh(propertySystemConfigsProvider(selectedPropertyId!)),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemConfigCard(PropertySystemConfig config) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getSystemIcon(config.systemType),
                  size: 32,
                  color: config.isEnabled ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        config.displayName ?? _getSystemDisplayName(config.systemType),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'System Type: ${config.systemType}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: config.isEnabled,
                  onChanged: (value) => _toggleSystemEnabled(config, value),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _editSystemConfig(config),
                    icon: const Icon(Icons.edit),
                    label: const Text('Edit Configuration'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _manageSystemContent(config),
                    icon: const Icon(Icons.content_copy),
                    label: const Text('Manage Content'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSystemIcon(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return Icons.water_drop;
      case 'ELECTRICITY':
        return Icons.electrical_services;
      case 'SECURITY':
        return Icons.security;
      case 'INTERNET':
        return Icons.wifi;
      case 'MAINTENANCE':
        return Icons.build;
      default:
        return Icons.settings;
    }
  }

  String _getSystemDisplayName(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return 'Water Management';
      case 'ELECTRICITY':
        return 'Electricity Management';
      case 'SECURITY':
        return 'Security Management';
      case 'INTERNET':
        return 'Internet Management';
      case 'MAINTENANCE':
        return 'Maintenance Management';
      default:
        return systemType;
    }
  }

  void _toggleSystemEnabled(PropertySystemConfig config, bool enabled) async {
    final managementNotifier = ref.read(propertySystemManagementProvider.notifier);
    
    final success = await managementNotifier.updateSystemConfig(
      propertyId: config.propertyId,
      configId: config.id,
      data: {
        'systemType': config.systemType,
        'isEnabled': enabled,
        'displayName': config.displayName,
        'displayOrder': config.displayOrder,
        'configuration': config.configuration,
      },
    );

    if (success) {
      ref.refresh(propertySystemConfigsProvider(selectedPropertyId!));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${config.displayName ?? config.systemType} ${enabled ? 'enabled' : 'disabled'}'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      final state = ref.read(propertySystemManagementProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.error ?? 'Failed to update system'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _editSystemConfig(PropertySystemConfig config) {
    showDialog(
      context: context,
      builder: (context) => _SystemConfigEditDialog(
        config: config,
        onSave: (updatedConfig) async {
          final managementNotifier = ref.read(propertySystemManagementProvider.notifier);
          
          final success = await managementNotifier.updateSystemConfig(
            propertyId: config.propertyId,
            configId: config.id,
            data: updatedConfig,
          );

          if (success) {
            ref.refresh(propertySystemConfigsProvider(selectedPropertyId!));
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('System configuration updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            final state = ref.read(propertySystemManagementProvider);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error ?? 'Failed to update configuration'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _manageSystemContent(PropertySystemConfig config) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EnhancedSystemContentManagementScreen(
          propertyId: config.propertyId,
          systemType: config.systemType,
          systemName: config.displayName ?? _getSystemDisplayName(config.systemType),
        ),
      ),
    );
  }
}

class _SystemConfigEditDialog extends StatefulWidget {
  final PropertySystemConfig config;
  final Function(Map<String, dynamic>) onSave;

  const _SystemConfigEditDialog({
    required this.config,
    required this.onSave,
  });

  @override
  State<_SystemConfigEditDialog> createState() => _SystemConfigEditDialogState();
}

class _SystemConfigEditDialogState extends State<_SystemConfigEditDialog> {
  late TextEditingController _displayNameController;
  late TextEditingController _displayOrderController;
  late bool _isEnabled;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController(text: widget.config.displayName);
    _displayOrderController = TextEditingController(text: widget.config.displayOrder?.toString() ?? '');
    _isEnabled = widget.config.isEnabled;
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _displayOrderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Edit ${widget.config.systemType} Configuration'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _displayNameController,
            decoration: const InputDecoration(
              labelText: 'Display Name',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _displayOrderController,
            decoration: const InputDecoration(
              labelText: 'Display Order',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Enabled'),
            value: _isEnabled,
            onChanged: (value) => setState(() => _isEnabled = value),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveConfig,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveConfig() {
    final data = {
      'systemType': widget.config.systemType,
      'isEnabled': _isEnabled,
      'displayName': _displayNameController.text.trim().isEmpty 
          ? null 
          : _displayNameController.text.trim(),
      'displayOrder': _displayOrderController.text.trim().isEmpty 
          ? null 
          : int.tryParse(_displayOrderController.text.trim()),
      'configuration': widget.config.configuration,
    };

    widget.onSave(data);
  }
}

// System Content Management Screen
class SystemContentManagementScreen extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final String systemName;

  const SystemContentManagementScreen({
    super.key,
    required this.propertyId,
    required this.systemType,
    required this.systemName,
  });

  @override
  State<SystemContentManagementScreen> createState() => _SystemContentManagementScreenState();
}

class _SystemContentManagementScreenState extends State<SystemContentManagementScreen> {
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;
  List<SystemContent> _systemContent = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSystemContent();
  }

  Future<void> _loadSystemContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _repository.getSystemContent(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
      );

      if (response.isSuccess && response.data != null) {
        setState(() {
          _systemContent = response.data!;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.message ?? 'Failed to load system content';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading system content: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.systemName} Content'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddContentDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSystemContent,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(_error!, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSystemContent,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_systemContent.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.content_paste, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No content available for ${widget.systemName}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddContentDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add Content'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadSystemContent,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _systemContent.length,
        itemBuilder: (context, index) {
          final content = _systemContent[index];
          return _buildContentCard(content);
        },
      ),
    );
  }

  Widget _buildContentCard(SystemContent content) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: _getContentTypeIcon(content.contentType),
        title: Text(
          content.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          _getContentTypeLabel(content.contentType),
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue),
              onPressed: () => _showEditContentDialog(content),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(content),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildContentDetails(content),
          ),
        ],
      ),
    );
  }

  Widget _buildContentDetails(SystemContent content) {
    final contentData = content.content as Map<String, dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...contentData.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildContentField(entry.key, entry.value),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildContentField(String key, dynamic value) {
    final formattedKey = key.replaceAll('_', ' ').toUpperCase();

    if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            formattedKey,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
          const SizedBox(height: 4),
          ...value.map((item) => Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 2),
            child: Text('• $item'),
          )).toList(),
        ],
      );
    } else if (value is Map) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            formattedKey,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
          const SizedBox(height: 4),
          ...value.entries.map((entry) => Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 2),
            child: Text('${entry.key}: ${entry.value}'),
          )).toList(),
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              formattedKey,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(value.toString()),
          ),
        ],
      );
    }
  }

  Icon _getContentTypeIcon(String contentType) {
    switch (contentType) {
      case 'contact':
        return const Icon(Icons.contact_phone, color: Colors.green);
      case 'maintenance_task':
        return const Icon(Icons.build, color: Colors.orange);
      case 'quality_parameter':
      case 'monitoring_parameter':
        return const Icon(Icons.analytics, color: Colors.blue);
      case 'service_info':
        return const Icon(Icons.tv, color: Colors.purple);
      default:
        return const Icon(Icons.info, color: Colors.grey);
    }
  }

  String _getContentTypeLabel(String contentType) {
    switch (contentType) {
      case 'contact':
        return 'Contact Information';
      case 'maintenance_task':
        return 'Maintenance Task';
      case 'quality_parameter':
        return 'Quality Parameter';
      case 'monitoring_parameter':
        return 'Monitoring Parameter';
      case 'service_info':
        return 'Service Information';
      default:
        return contentType.replaceAll('_', ' ').toUpperCase();
    }
  }

  void _showAddContentDialog() {
    showDialog(
      context: context,
      builder: (context) => SystemContentEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemContent();
        },
      ),
    );
  }

  void _showEditContentDialog(SystemContent content) {
    showDialog(
      context: context,
      builder: (context) => SystemContentEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        content: content,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemContent();
        },
      ),
    );
  }

  void _showDeleteConfirmation(SystemContent content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Content'),
        content: Text('Are you sure you want to delete "${content.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteContent(content);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteContent(SystemContent content) async {
    try {
      final response = await _repository.deleteSystemContent(
        propertyId: widget.propertyId,
        contentId: content.id,
      );

      if (response.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Content deleted successfully')),
        );
        _loadSystemContent();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete content: ${response.message}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting content: $e')),
      );
    }
  }
}

// System Content Edit Dialog
class SystemContentEditDialog extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final SystemContent? content;
  final VoidCallback onSaved;

  const SystemContentEditDialog({
    super.key,
    required this.propertyId,
    required this.systemType,
    this.content,
    required this.onSaved,
  });

  @override
  State<SystemContentEditDialog> createState() => _SystemContentEditDialogState();
}

class _SystemContentEditDialogState extends State<SystemContentEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;

  late TextEditingController _titleController;
  late String _selectedContentType;
  late Map<String, dynamic> _contentData;
  bool _isLoading = false;

  final List<String> _contentTypes = [
    'contact',
    'maintenance_task',
    'quality_parameter',
    'monitoring_parameter',
    'service_info',
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.content?.title ?? '');
    _selectedContentType = widget.content?.contentType ?? _contentTypes.first;
    _contentData = Map<String, dynamic>.from(widget.content?.content as Map<String, dynamic>? ?? {});
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.content == null ? 'Add System Content' : 'Edit System Content',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'Title',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a title';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: _selectedContentType,
                        decoration: const InputDecoration(
                          labelText: 'Content Type',
                          border: OutlineInputBorder(),
                        ),
                        items: _contentTypes.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(_getContentTypeLabel(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedContentType = value!;
                            _contentData = _getDefaultContentForType(value);
                          });
                        },
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Content Details',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      _buildContentFields(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveContent,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.content == null ? 'Add' : 'Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentFields() {
    return Column(
      children: _contentData.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildFieldEditor(entry.key, entry.value),
        );
      }).toList(),
    );
  }

  Widget _buildFieldEditor(String key, dynamic value) {
    if (value is String) {
      return TextFormField(
        initialValue: value,
        decoration: InputDecoration(
          labelText: key.replaceAll('_', ' ').toUpperCase(),
          border: const OutlineInputBorder(),
        ),
        onChanged: (newValue) {
          _contentData[key] = newValue;
        },
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(key.replaceAll('_', ' ').toUpperCase()),
          const SizedBox(height: 8),
          ...value.asMap().entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TextFormField(
                initialValue: entry.value.toString(),
                decoration: InputDecoration(
                  labelText: 'Item ${entry.key + 1}',
                  border: const OutlineInputBorder(),
                ),
                onChanged: (newValue) {
                  final list = List.from(_contentData[key]);
                  list[entry.key] = newValue;
                  _contentData[key] = list;
                },
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return TextFormField(
        initialValue: value.toString(),
        decoration: InputDecoration(
          labelText: key.replaceAll('_', ' ').toUpperCase(),
          border: const OutlineInputBorder(),
        ),
        onChanged: (newValue) {
          _contentData[key] = newValue;
        },
      );
    }
  }

  String _getContentTypeLabel(String contentType) {
    switch (contentType) {
      case 'contact':
        return 'Contact Information';
      case 'maintenance_task':
        return 'Maintenance Task';
      case 'quality_parameter':
        return 'Quality Parameter';
      case 'monitoring_parameter':
        return 'Monitoring Parameter';
      case 'service_info':
        return 'Service Information';
      default:
        return contentType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Map<String, dynamic> _getDefaultContentForType(String contentType) {
    switch (contentType) {
      case 'contact':
        return {
          'organization': '',
          'phone': '',
          'email': '',
          'address': '',
          'workingHours': '',
          'services': <String>[],
        };
      case 'maintenance_task':
        return {
          'description': '',
          'frequency': '',
          'duration': '',
          'requirements': <String>[],
          'steps': <String>[],
        };
      case 'quality_parameter':
      case 'monitoring_parameter':
        return {
          'parameter': '',
          'normalRange': '',
          'testFrequency': '',
          'alertThreshold': '',
        };
      case 'service_info':
        return {
          'serviceName': '',
          'subscriptionType': '',
          'monthlyFee': '',
          'features': <String>[],
        };
      default:
        return {};
    }
  }

  Future<void> _saveContent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final contentData = {
        'systemType': widget.systemType,
        'contentType': _selectedContentType,
        'title': _titleController.text,
        'content': _contentData,
        'displayOrder': widget.content?.displayOrder ?? 1,
      };

      ApiResponse response;
      if (widget.content == null) {
        response = await _repository.createSystemContent(
          propertyId: widget.propertyId,
          data: contentData,
        );
      } else {
        response = await _repository.updateSystemContent(
          propertyId: widget.propertyId,
          contentId: widget.content!.id,
          data: contentData,
        );
      }

      if (response.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.content == null
                ? 'Content added successfully'
                : 'Content updated successfully'),
          ),
        );
        widget.onSaved();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save content: ${response.message}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving content: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
