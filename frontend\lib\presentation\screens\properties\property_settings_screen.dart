import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/repositories/property_repository.dart' show PropertyDetail;
import '../../providers/properties_providers.dart';
import '../admin/enhanced_system_content_management_screen.dart';
import '../../../data/models/system.dart';
import '../../providers/property_system_providers.dart';

class PropertySettingsScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const PropertySettingsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertySettingsScreen> createState() => _PropertySettingsScreenState();
}

class _PropertySettingsScreenState extends ConsumerState<PropertySettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertyDetailAsync = ref.watch(propertyDetailProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: propertyDetailAsync.when(
          data: (property) => Text('${property?.name ?? 'Property'} Settings'),
          loading: () => const Text('Property Settings'),
          error: (_, __) => const Text('Property Settings'),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Systems'),
            Tab(icon: Icon(Icons.edit_note), text: 'Content'),
            Tab(icon: Icon(Icons.settings), text: 'Config'),
            Tab(icon: Icon(Icons.security), text: 'Access'),
          ],
        ),
      ),
      body: propertyDetailAsync.when(
        data: (property) {
          if (property == null) {
            return _buildPropertyNotFound();
          }
          return TabBarView(
            controller: _tabController,
            children: [
              _buildSystemsTab(property),
              _buildContentTab(property),
              _buildConfigTab(property),
              _buildAccessTab(property),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildPropertyNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Property not found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading property settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsTab(PropertyDetail property) {
    final systemConfigsAsync = ref.watch(propertySystemConfigsProvider(widget.propertyId));

    return systemConfigsAsync.when(
      data: (configs) => _buildSystemsList(configs),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red[400], size: 48),
            const SizedBox(height: 16),
            Text('Error loading systems: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(propertySystemConfigsProvider(widget.propertyId)),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemsList(List<PropertySystemConfig> configs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: configs.length,
      itemBuilder: (context, index) {
        final config = configs[index];
        return _buildSystemCard(config);
      },
    );
  }

  Widget _buildSystemCard(PropertySystemConfig config) {
    IconData systemIcon;
    switch (config.systemType) {
      case 'WATER':
        systemIcon = Icons.water_drop;
        break;
      case 'ELECTRICITY':
        systemIcon = Icons.electrical_services;
        break;
      case 'SECURITY':
        systemIcon = Icons.security;
        break;
      case 'INTERNET':
        systemIcon = Icons.wifi;
        break;
      case 'OTT':
        systemIcon = Icons.tv;
        break;
      case 'MAINTENANCE':
        systemIcon = Icons.build;
        break;
      default:
        systemIcon = Icons.settings;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(systemIcon, color: Colors.blue[600]),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    config.displayName ?? config.systemType,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Switch(
                  value: config.isEnabled,
                  onChanged: (value) => _toggleSystemEnabled(config, value),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _editSystemConfig(config),
                    icon: const Icon(Icons.edit),
                    label: const Text('Configure'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _manageSystemContent(config),
                    icon: const Icon(Icons.content_copy),
                    label: const Text('Manage Content'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTab(PropertyDetail property) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.edit_note, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Content Management',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Manage content for individual systems using the Systems tab',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildConfigTab(PropertyDetail property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Property Configuration',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Basic Information
          _buildConfigSection(
            'Basic Information',
            [
              _buildConfigItem('Property Name', property.name, Icons.business),
              _buildConfigItem('Address', property.address, Icons.location_on),
              _buildConfigItem('Type', property.type, Icons.category),
              _buildConfigItem('Status', property.status, Icons.info),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Notification Settings
          _buildConfigSection(
            'Notifications',
            [
              _buildSwitchItem('Email Alerts', true, Icons.email),
              _buildSwitchItem('SMS Notifications', false, Icons.sms),
              _buildSwitchItem('Push Notifications', true, Icons.notifications),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Monitoring Settings
          _buildConfigSection(
            'Monitoring',
            [
              _buildConfigItem('Check Interval', '5 minutes', Icons.timer),
              _buildConfigItem('Alert Threshold', '85%', Icons.warning),
              _buildConfigItem('Maintenance Window', '2:00 AM - 4:00 AM', Icons.schedule),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccessTab(PropertyDetail property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Access Control',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Assigned Users
          _buildConfigSection(
            'Assigned Users',
            [
              _buildUserItem('John Doe', 'Property Manager', Icons.person),
              _buildUserItem('Jane Smith', 'Maintenance Staff', Icons.build),
              _buildUserItem('Mike Johnson', 'Security Personnel', Icons.security),
            ],
          ),
          
          const SizedBox(height: 16),
          
          ElevatedButton.icon(
            onPressed: () {
              // TODO: Navigate to user assignment screen
            },
            icon: const Icon(Icons.person_add),
            label: const Text('Assign Users'),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.blue[700],
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildConfigItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchItem(String label, bool value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              // TODO: Handle switch change
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(String name, String role, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  role,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Edit user access
            },
            icon: const Icon(Icons.edit, size: 20),
          ),
        ],
      ),
    );
  }

  void _toggleSystemEnabled(PropertySystemConfig config, bool enabled) {
    // TODO: Implement system enable/disable
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${config.systemType} ${enabled ? 'enabled' : 'disabled'}'),
      ),
    );
  }

  void _editSystemConfig(PropertySystemConfig config) {
    // TODO: Show system configuration dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${config.systemType} configuration'),
      ),
    );
  }

  void _manageSystemContent(PropertySystemConfig config) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EnhancedSystemContentManagementScreen(
          propertyId: widget.propertyId,
          systemType: config.systemType,
          systemName: config.displayName ?? config.systemType,
        ),
      ),
    );
  }
}
