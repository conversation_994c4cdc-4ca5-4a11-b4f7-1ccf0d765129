import 'package:json_annotation/json_annotation.dart';

part 'system.g.dart';

// System Status Model
@JsonSerializable()
class SystemStatus {
  final String id;
  final String propertyId;
  final String systemType;
  final String status;
  final int healthScore;
  final String? description;
  final String lastChecked;
  final Map<String, dynamic>? metadata;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const SystemStatus({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.status,
    required this.healthScore,
    this.description,
    required this.lastChecked,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory SystemStatus.fromJson(Map<String, dynamic> json) => _$SystemStatusFromJson(json);
  Map<String, dynamic> toJson() => _$SystemStatusToJson(this);

  // Helper getters
  bool get isOperational => status == 'OPERATIONAL';
  bool get isWarning => status == 'WARNING';
  bool get isCritical => status == 'CRITICAL';
  bool get isOffline => status == 'OFFLINE';

  DateTime get lastCheckedDateTime => DateTime.parse(lastChecked);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  String get propertyName => property?.name ?? 'Unknown Property';
}

// Water System Model
@JsonSerializable()
class WaterSystem {
  final String id;
  final String propertyId;
  final String tankName;
  final double capacity;
  final double currentLevel;
  final double levelPercentage;
  final String pumpStatus;
  final double? flowRate;
  final double? pressure;
  final String? quality;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const WaterSystem({
    required this.id,
    required this.propertyId,
    required this.tankName,
    required this.capacity,
    required this.currentLevel,
    required this.levelPercentage,
    required this.pumpStatus,
    this.flowRate,
    this.pressure,
    this.quality,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory WaterSystem.fromJson(Map<String, dynamic> json) => _$WaterSystemFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemToJson(this);

  // Helper getters
  bool get isPumpOn => pumpStatus == 'ON';
  bool get isPumpOff => pumpStatus == 'OFF';
  bool get isPumpMaintenance => pumpStatus == 'MAINTENANCE';

  bool get isLevelCritical => levelPercentage < 10;
  bool get isLevelWarning => levelPercentage < 30 && levelPercentage >= 10;
  bool get isLevelNormal => levelPercentage >= 30;

  bool get isQualityGood => quality == 'GOOD';
  bool get isQualityFair => quality == 'FAIR';
  bool get isQualityPoor => quality == 'POOR';

  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Electricity System Model
@JsonSerializable()
class ElectricitySystem {
  final String id;
  final String propertyId;
  final String systemName;
  final String generatorStatus;
  final double? fuelLevel;
  final double? powerConsumption;
  final double? voltage;
  final double? frequency;
  final double? loadPercentage;
  final String mainsPowerStatus;
  final double? batteryBackup;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;

  const ElectricitySystem({
    required this.id,
    required this.propertyId,
    required this.systemName,
    required this.generatorStatus,
    this.fuelLevel,
    this.powerConsumption,
    this.voltage,
    this.frequency,
    this.loadPercentage,
    required this.mainsPowerStatus,
    this.batteryBackup,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory ElectricitySystem.fromJson(Map<String, dynamic> json) => _$ElectricitySystemFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemToJson(this);

  // Helper getters
  bool get isGeneratorOn => generatorStatus == 'ON';
  bool get isGeneratorOff => generatorStatus == 'OFF';
  bool get isGeneratorMaintenance => generatorStatus == 'MAINTENANCE';
  bool get isGeneratorStandby => generatorStatus == 'STANDBY';

  bool get isMainsPowerAvailable => mainsPowerStatus == 'AVAILABLE';
  bool get isMainsPowerUnavailable => mainsPowerStatus == 'UNAVAILABLE';

  bool get isFuelCritical => fuelLevel != null && fuelLevel! < 10;
  bool get isFuelWarning => fuelLevel != null && fuelLevel! < 30 && fuelLevel! >= 10;
  bool get isFuelNormal => fuelLevel != null && fuelLevel! >= 30;

  bool get isBatteryCritical => batteryBackup != null && batteryBackup! < 20;
  bool get isBatteryWarning => batteryBackup != null && batteryBackup! < 50 && batteryBackup! >= 20;
  bool get isBatteryNormal => batteryBackup != null && batteryBackup! >= 50;

  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Security System Model
@JsonSerializable()
class SecuritySystem {
  final String id;
  final String propertyId;
  final String systemName;
  final int cameraCount;
  final int activeCameras;
  final int accessPoints;
  final int activeAccess;
  final String alarmStatus;
  final bool motionDetected;
  final String? lastIncident;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final bool isActive;
  final String createdAt;
  final String updatedAt;
  final PropertyInfo? property;
  final List<SecurityCamera> cameras;

  const SecuritySystem({
    required this.id,
    required this.propertyId,
    required this.systemName,
    required this.cameraCount,
    required this.activeCameras,
    required this.accessPoints,
    required this.activeAccess,
    required this.alarmStatus,
    required this.motionDetected,
    this.lastIncident,
    this.lastMaintenance,
    this.nextMaintenance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.property,
    required this.cameras,
  });

  factory SecuritySystem.fromJson(Map<String, dynamic> json) => _$SecuritySystemFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemToJson(this);

  // Helper getters
  bool get isAlarmArmed => alarmStatus == 'ARMED';
  bool get isAlarmDisarmed => alarmStatus == 'DISARMED';
  bool get isAlarmTriggered => alarmStatus == 'TRIGGERED';
  bool get isAlarmMaintenance => alarmStatus == 'MAINTENANCE';

  double get cameraOperationalRate => cameraCount > 0 ? (activeCameras / cameraCount) * 100 : 0;
  double get accessOperationalRate => accessPoints > 0 ? (activeAccess / accessPoints) * 100 : 0;

  bool get hasCameraIssues => activeCameras < cameraCount;
  bool get hasAccessIssues => activeAccess < accessPoints;

  DateTime? get lastIncidentDateTime => lastIncident != null ? DateTime.parse(lastIncident!) : null;
  DateTime? get lastMaintenanceDateTime => lastMaintenance != null ? DateTime.parse(lastMaintenance!) : null;
  DateTime? get nextMaintenanceDateTime => nextMaintenance != null ? DateTime.parse(nextMaintenance!) : null;

  bool get needsMaintenance => nextMaintenanceDateTime != null && nextMaintenanceDateTime!.isBefore(DateTime.now());
}

// Security Camera Model
@JsonSerializable()
class SecurityCamera {
  final String id;
  final String securitySystemId;
  final String cameraName;
  final String location;
  final String status;
  final String? ipAddress;
  final String recordingStatus;
  final String? lastPing;
  final String createdAt;
  final String updatedAt;

  const SecurityCamera({
    required this.id,
    required this.securitySystemId,
    required this.cameraName,
    required this.location,
    required this.status,
    this.ipAddress,
    required this.recordingStatus,
    this.lastPing,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SecurityCamera.fromJson(Map<String, dynamic> json) => _$SecurityCameraFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityCameraToJson(this);

  // Helper getters
  bool get isOnline => status == 'ONLINE';
  bool get isOffline => status == 'OFFLINE';
  bool get isMaintenance => status == 'MAINTENANCE';

  bool get isRecording => recordingStatus == 'RECORDING';
  bool get isRecordingStopped => recordingStatus == 'STOPPED';
  bool get isRecordingError => recordingStatus == 'ERROR';

  DateTime? get lastPingDateTime => lastPing != null ? DateTime.parse(lastPing!) : null;
}

// Response Models
@JsonSerializable()
class WaterSystemsResponse {
  final List<WaterSystem> systems;
  final WaterSystemsSummary summary;

  const WaterSystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory WaterSystemsResponse.fromJson(Map<String, dynamic> json) => _$WaterSystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemsResponseToJson(this);

  factory WaterSystemsResponse.empty() {
    return const WaterSystemsResponse(
      systems: [],
      summary: WaterSystemsSummary(
        totalSystems: 0,
        totalCapacity: 0,
        totalCurrentLevel: 0,
        averageLevel: 0,
        activePumps: 0,
        maintenanceRequired: 0,
      ),
    );
  }
}

@JsonSerializable()
class WaterSystemsSummary {
  final int totalSystems;
  final double totalCapacity;
  final double totalCurrentLevel;
  final int averageLevel;
  final int activePumps;
  final int maintenanceRequired;

  const WaterSystemsSummary({
    required this.totalSystems,
    required this.totalCapacity,
    required this.totalCurrentLevel,
    required this.averageLevel,
    required this.activePumps,
    required this.maintenanceRequired,
  });

  factory WaterSystemsSummary.fromJson(Map<String, dynamic> json) => _$WaterSystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemsSummaryToJson(this);
}

@JsonSerializable()
class ElectricitySystemsResponse {
  final List<ElectricitySystem> systems;
  final ElectricitySystemsSummary summary;

  const ElectricitySystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory ElectricitySystemsResponse.fromJson(Map<String, dynamic> json) => _$ElectricitySystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemsResponseToJson(this);

  factory ElectricitySystemsResponse.empty() {
    return const ElectricitySystemsResponse(
      systems: [],
      summary: ElectricitySystemsSummary(
        totalSystems: 0,
        totalPowerConsumption: 0,
        averageFuelLevel: 0,
        activeGenerators: 0,
        mainsAvailable: 0,
        maintenanceRequired: 0,
      ),
    );
  }
}

@JsonSerializable()
class ElectricitySystemsSummary {
  final int totalSystems;
  final double totalPowerConsumption;
  final int averageFuelLevel;
  final int activeGenerators;
  final int mainsAvailable;
  final int maintenanceRequired;

  const ElectricitySystemsSummary({
    required this.totalSystems,
    required this.totalPowerConsumption,
    required this.averageFuelLevel,
    required this.activeGenerators,
    required this.mainsAvailable,
    required this.maintenanceRequired,
  });

  factory ElectricitySystemsSummary.fromJson(Map<String, dynamic> json) => _$ElectricitySystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricitySystemsSummaryToJson(this);
}

@JsonSerializable()
class SecuritySystemsResponse {
  final List<SecuritySystem> systems;
  final SecuritySystemsSummary summary;

  const SecuritySystemsResponse({
    required this.systems,
    required this.summary,
  });

  factory SecuritySystemsResponse.fromJson(Map<String, dynamic> json) => _$SecuritySystemsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemsResponseToJson(this);

  factory SecuritySystemsResponse.empty() {
    return const SecuritySystemsResponse(
      systems: [],
      summary: SecuritySystemsSummary(
        totalSystems: 0,
        totalCameras: 0,
        totalActiveCameras: 0,
        totalAccessPoints: 0,
        totalActiveAccess: 0,
        armedSystems: 0,
        triggeredAlarms: 0,
        maintenanceRequired: 0,
        cameraOperationalRate: 0,
        accessOperationalRate: 0,
      ),
    );
  }
}

@JsonSerializable()
class SecuritySystemsSummary {
  final int totalSystems;
  final int totalCameras;
  final int totalActiveCameras;
  final int totalAccessPoints;
  final int totalActiveAccess;
  final int armedSystems;
  final int triggeredAlarms;
  final int maintenanceRequired;
  final int cameraOperationalRate;
  final int accessOperationalRate;

  const SecuritySystemsSummary({
    required this.totalSystems,
    required this.totalCameras,
    required this.totalActiveCameras,
    required this.totalAccessPoints,
    required this.totalActiveAccess,
    required this.armedSystems,
    required this.triggeredAlarms,
    required this.maintenanceRequired,
    required this.cameraOperationalRate,
    required this.accessOperationalRate,
  });

  factory SecuritySystemsSummary.fromJson(Map<String, dynamic> json) => _$SecuritySystemsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySystemsSummaryToJson(this);
}

// Property Info Model (shared)
@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String? type;
  final String? address;

  const PropertyInfo({
    required this.id,
    required this.name,
    this.type,
    this.address,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

// Water Consumption Models
@JsonSerializable()
class WaterConsumption {
  final double todayUsage;
  final double weekUsage;
  final double monthUsage;
  final double averageDailyUsage;
  final double todayUsageChange;
  final double weekUsageChange;
  final double monthUsageChange;
  final List<DailyUsage> dailyUsage;
  final List<TankUsage> usageByTank;

  const WaterConsumption({
    required this.todayUsage,
    required this.weekUsage,
    required this.monthUsage,
    required this.averageDailyUsage,
    required this.todayUsageChange,
    required this.weekUsageChange,
    required this.monthUsageChange,
    required this.dailyUsage,
    required this.usageByTank,
  });

  factory WaterConsumption.fromJson(Map<String, dynamic> json) => _$WaterConsumptionFromJson(json);
  Map<String, dynamic> toJson() => _$WaterConsumptionToJson(this);

  factory WaterConsumption.empty() {
    return const WaterConsumption(
      todayUsage: 0,
      weekUsage: 0,
      monthUsage: 0,
      averageDailyUsage: 0,
      todayUsageChange: 0,
      weekUsageChange: 0,
      monthUsageChange: 0,
      dailyUsage: [],
      usageByTank: [],
    );
  }
}

@JsonSerializable()
class DailyUsage {
  final String date;
  final double usage;

  const DailyUsage({
    required this.date,
    required this.usage,
  });

  factory DailyUsage.fromJson(Map<String, dynamic> json) => _$DailyUsageFromJson(json);
  Map<String, dynamic> toJson() => _$DailyUsageToJson(this);
}

@JsonSerializable()
class TankUsage {
  final String tankName;
  final double usage;
  final double usagePercentage;

  const TankUsage({
    required this.tankName,
    required this.usage,
    required this.usagePercentage,
  });

  factory TankUsage.fromJson(Map<String, dynamic> json) => _$TankUsageFromJson(json);
  Map<String, dynamic> toJson() => _$TankUsageToJson(this);
}

// Water Maintenance Models
@JsonSerializable()
class WaterMaintenance {
  final int overdueTasks;
  final int dueSoonTasks;
  final int completedTasks;
  final List<MaintenanceTask> upcomingMaintenance;
  final List<MaintenanceRecord> maintenanceHistory;
  final List<MaintenanceTask> tasks;

  const WaterMaintenance({
    required this.overdueTasks,
    required this.dueSoonTasks,
    required this.completedTasks,
    required this.upcomingMaintenance,
    required this.maintenanceHistory,
    required this.tasks,
  });

  factory WaterMaintenance.fromJson(Map<String, dynamic> json) => _$WaterMaintenanceFromJson(json);
  Map<String, dynamic> toJson() => _$WaterMaintenanceToJson(this);

  factory WaterMaintenance.empty() {
    return const WaterMaintenance(
      overdueTasks: 0,
      dueSoonTasks: 0,
      completedTasks: 0,
      upcomingMaintenance: [],
      maintenanceHistory: [],
      tasks: [],
    );
  }
}

@JsonSerializable()
class MaintenanceTask {
  final String id;
  final String title;
  final String description;
  final String type;
  final DateTime dueDate;
  final String priority;
  final String status;
  final String? frequency;
  final DateTime? lastCompleted;

  const MaintenanceTask({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.dueDate,
    required this.priority,
    required this.status,
    this.frequency,
    this.lastCompleted,
  });

  factory MaintenanceTask.fromJson(Map<String, dynamic> json) => _$MaintenanceTaskFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceTaskToJson(this);
}

@JsonSerializable()
class MaintenanceRecord {
  final String id;
  final String title;
  final String type;
  final DateTime completedDate;
  final double? cost;
  final String technician;
  final String notes;

  const MaintenanceRecord({
    required this.id,
    required this.title,
    required this.type,
    required this.completedDate,
    this.cost,
    required this.technician,
    required this.notes,
  });

  factory MaintenanceRecord.fromJson(Map<String, dynamic> json) => _$MaintenanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$MaintenanceRecordToJson(this);
}

// Water Quality Models
@JsonSerializable()
class WaterQuality {
  final String overallRating;
  final DateTime lastTested;
  final List<String> alerts;
  final List<QualityParameter> parameters;
  final List<QualityTest> history;

  const WaterQuality({
    required this.overallRating,
    required this.lastTested,
    required this.alerts,
    required this.parameters,
    required this.history,
  });

  factory WaterQuality.fromJson(Map<String, dynamic> json) => _$WaterQualityFromJson(json);
  Map<String, dynamic> toJson() => _$WaterQualityToJson(this);

  factory WaterQuality.empty() {
    return WaterQuality(
      overallRating: 'UNKNOWN',
      lastTested: DateTime.now(),
      alerts: [],
      parameters: [],
      history: [],
    );
  }
}

@JsonSerializable()
class QualityParameter {
  final String name;
  final double value;
  final double minValue;
  final double maxValue;
  final String unit;

  const QualityParameter({
    required this.name,
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.unit,
  });

  factory QualityParameter.fromJson(Map<String, dynamic> json) => _$QualityParameterFromJson(json);
  Map<String, dynamic> toJson() => _$QualityParameterToJson(this);
}

@JsonSerializable()
class QualityTest {
  final String id;
  final DateTime testDate;
  final String result;
  final String notes;
  final String technician;

  const QualityTest({
    required this.id,
    required this.testDate,
    required this.result,
    required this.notes,
    required this.technician,
  });

  factory QualityTest.fromJson(Map<String, dynamic> json) => _$QualityTestFromJson(json);
  Map<String, dynamic> toJson() => _$QualityTestToJson(this);
}

@JsonSerializable()
class WaterContact {
  final String id;
  final String name;
  final String organization;
  final String phone;
  final String email;
  final String type;

  const WaterContact({
    required this.id,
    required this.name,
    required this.organization,
    required this.phone,
    required this.email,
    required this.type,
  });

  factory WaterContact.fromJson(Map<String, dynamic> json) => _$WaterContactFromJson(json);
  Map<String, dynamic> toJson() => _$WaterContactToJson(this);
}

// Property System Configuration Models
@JsonSerializable()
class PropertySystemConfig {
  final String id;
  final String propertyId;
  final String systemType;
  final bool isEnabled;
  final String? displayName;
  final int? displayOrder;
  final Map<String, dynamic>? configuration;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PropertySystemConfig({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.isEnabled,
    this.displayName,
    this.displayOrder,
    this.configuration,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PropertySystemConfig.fromJson(Map<String, dynamic> json) {
    try {
      return PropertySystemConfig(
        id: json['id'] as String,
        propertyId: json['propertyId'] as String,
        systemType: json['systemType'] as String,
        isEnabled: json['isEnabled'] as bool,
        displayName: json['displayName'] as String?,
        displayOrder: json['displayOrder'] is int
            ? json['displayOrder'] as int?
            : int.tryParse(json['displayOrder']?.toString() ?? ''),
        configuration: json['configuration'] as Map<String, dynamic>?,
        createdAt: DateTime.parse(json['createdAt'] as String),
        updatedAt: DateTime.parse(json['updatedAt'] as String),
      );
    } catch (e) {
      print('Error parsing PropertySystemConfig: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$PropertySystemConfigToJson(this);
}

@JsonSerializable()
class SystemContent {
  final String id;
  final String propertyId;
  final String systemType;
  final String contentType;
  final String title;
  final Map<String, dynamic> content;
  final String? richContent; // Large text content for rich text editor
  final String? contentFormat; // 'markdown', 'html', 'json'
  final int? displayOrder;
  final bool isActive;
  final bool isTab; // Indicates if this content represents a tab
  final String? tabIcon; // Icon for tab
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SystemContent({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.contentType,
    required this.title,
    required this.content,
    this.richContent,
    this.contentFormat,
    this.displayOrder,
    required this.isActive,
    this.isTab = false,
    this.tabIcon,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SystemContent.fromJson(Map<String, dynamic> json) => _$SystemContentFromJson(json);
  Map<String, dynamic> toJson() => _$SystemContentToJson(this);
}

@JsonSerializable()
class SystemContact {
  final String id;
  final String propertyId;
  final String systemType;
  final String name;
  final String? organization;
  final String phone;
  final String? email;
  final String contactType;
  final bool isActive;
  final int? displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SystemContact({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.name,
    this.organization,
    required this.phone,
    this.email,
    required this.contactType,
    required this.isActive,
    this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SystemContact.fromJson(Map<String, dynamic> json) => _$SystemContactFromJson(json);
  Map<String, dynamic> toJson() => _$SystemContactToJson(this);
}

// Security Models
@JsonSerializable()
class AccessControl {
  final int totalAccessPoints;
  final int activeAccessPoints;
  final int todayEvents;
  final List<AccessEvent> recentEvents;
  final List<AccessPoint> accessPoints;

  const AccessControl({
    required this.totalAccessPoints,
    required this.activeAccessPoints,
    required this.todayEvents,
    required this.recentEvents,
    required this.accessPoints,
  });

  factory AccessControl.fromJson(Map<String, dynamic> json) => _$AccessControlFromJson(json);
  Map<String, dynamic> toJson() => _$AccessControlToJson(this);

  factory AccessControl.empty() {
    return const AccessControl(
      totalAccessPoints: 0,
      activeAccessPoints: 0,
      todayEvents: 0,
      recentEvents: [],
      accessPoints: [],
    );
  }
}

@JsonSerializable()
class AccessEvent {
  final String id;
  final String eventType;
  final String description;
  final String location;
  final DateTime timestamp;
  final String? userId;
  final String? userName;

  const AccessEvent({
    required this.id,
    required this.eventType,
    required this.description,
    required this.location,
    required this.timestamp,
    this.userId,
    this.userName,
  });

  factory AccessEvent.fromJson(Map<String, dynamic> json) => _$AccessEventFromJson(json);
  Map<String, dynamic> toJson() => _$AccessEventToJson(this);
}

@JsonSerializable()
class AccessPoint {
  final String id;
  final String name;
  final String location;
  final String type;
  final String status;
  final bool isLocked;
  final DateTime? lastAccess;

  const AccessPoint({
    required this.id,
    required this.name,
    required this.location,
    required this.type,
    required this.status,
    required this.isLocked,
    this.lastAccess,
  });

  factory AccessPoint.fromJson(Map<String, dynamic> json) => _$AccessPointFromJson(json);
  Map<String, dynamic> toJson() => _$AccessPointToJson(this);
}

@JsonSerializable()
class SecurityIncidents {
  final int openIncidents;
  final int todayIncidents;
  final int weeklyResolved;
  final List<SecurityIncident> recentIncidents;
  final IncidentStatistics statistics;

  const SecurityIncidents({
    required this.openIncidents,
    required this.todayIncidents,
    required this.weeklyResolved,
    required this.recentIncidents,
    required this.statistics,
  });

  factory SecurityIncidents.fromJson(Map<String, dynamic> json) => _$SecurityIncidentsFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityIncidentsToJson(this);

  factory SecurityIncidents.empty() {
    return SecurityIncidents(
      openIncidents: 0,
      todayIncidents: 0,
      weeklyResolved: 0,
      recentIncidents: [],
      statistics: IncidentStatistics.empty(),
    );
  }
}

@JsonSerializable()
class SecurityIncident {
  final String id;
  final String title;
  final String description;
  final String type;
  final String severity;
  final String status;
  final String location;
  final DateTime timestamp;
  final String? reportedBy;
  final DateTime? resolvedAt;

  const SecurityIncident({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.severity,
    required this.status,
    required this.location,
    required this.timestamp,
    this.reportedBy,
    this.resolvedAt,
  });

  factory SecurityIncident.fromJson(Map<String, dynamic> json) => _$SecurityIncidentFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityIncidentToJson(this);
}

@JsonSerializable()
class IncidentStatistics {
  final Map<String, int> byType;
  final Map<String, int> bySeverity;
  final Map<String, int> byStatus;

  const IncidentStatistics({
    required this.byType,
    required this.bySeverity,
    required this.byStatus,
  });

  factory IncidentStatistics.fromJson(Map<String, dynamic> json) => _$IncidentStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$IncidentStatisticsToJson(this);

  factory IncidentStatistics.empty() {
    return const IncidentStatistics(
      byType: {},
      bySeverity: {},
      byStatus: {},
    );
  }
}

@JsonSerializable()
class SecurityMaintenance {
  final int overdueTasks;
  final int dueSoonTasks;
  final int completedTasks;
  final List<SecurityMaintenanceTask> upcomingMaintenance;
  final List<SecurityMaintenanceRecord> maintenanceHistory;

  const SecurityMaintenance({
    required this.overdueTasks,
    required this.dueSoonTasks,
    required this.completedTasks,
    required this.upcomingMaintenance,
    required this.maintenanceHistory,
  });

  factory SecurityMaintenance.fromJson(Map<String, dynamic> json) => _$SecurityMaintenanceFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityMaintenanceToJson(this);

  factory SecurityMaintenance.empty() {
    return const SecurityMaintenance(
      overdueTasks: 0,
      dueSoonTasks: 0,
      completedTasks: 0,
      upcomingMaintenance: [],
      maintenanceHistory: [],
    );
  }
}

@JsonSerializable()
class SecurityMaintenanceTask {
  final String id;
  final String title;
  final String description;
  final String type;
  final DateTime dueDate;
  final String priority;
  final String status;
  final String? assignedTo;

  const SecurityMaintenanceTask({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.dueDate,
    required this.priority,
    required this.status,
    this.assignedTo,
  });

  factory SecurityMaintenanceTask.fromJson(Map<String, dynamic> json) => _$SecurityMaintenanceTaskFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityMaintenanceTaskToJson(this);
}

@JsonSerializable()
class SecurityMaintenanceRecord {
  final String id;
  final String title;
  final String type;
  final DateTime completedDate;
  final double? cost;
  final String technician;
  final String notes;

  const SecurityMaintenanceRecord({
    required this.id,
    required this.title,
    required this.type,
    required this.completedDate,
    this.cost,
    required this.technician,
    required this.notes,
  });

  factory SecurityMaintenanceRecord.fromJson(Map<String, dynamic> json) => _$SecurityMaintenanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityMaintenanceRecordToJson(this);
}

// Electricity Models
@JsonSerializable()
class PowerAnalytics {
  final double totalConsumption;
  final double averageConsumption;
  final double peakConsumption;
  final double costToday;
  final double costThisMonth;
  final double efficiencyRating;
  final List<PowerUsageData> hourlyUsage;
  final List<PowerUsageData> dailyUsage;
  final List<GeneratorUsage> generatorUsage;

  const PowerAnalytics({
    required this.totalConsumption,
    required this.averageConsumption,
    required this.peakConsumption,
    required this.costToday,
    required this.costThisMonth,
    required this.efficiencyRating,
    required this.hourlyUsage,
    required this.dailyUsage,
    required this.generatorUsage,
  });

  factory PowerAnalytics.fromJson(Map<String, dynamic> json) => _$PowerAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$PowerAnalyticsToJson(this);

  factory PowerAnalytics.empty() {
    return const PowerAnalytics(
      totalConsumption: 0,
      averageConsumption: 0,
      peakConsumption: 0,
      costToday: 0,
      costThisMonth: 0,
      efficiencyRating: 0,
      hourlyUsage: [],
      dailyUsage: [],
      generatorUsage: [],
    );
  }
}

@JsonSerializable()
class PowerUsageData {
  final String timestamp;
  final double consumption;
  final double cost;

  const PowerUsageData({
    required this.timestamp,
    required this.consumption,
    required this.cost,
  });

  factory PowerUsageData.fromJson(Map<String, dynamic> json) => _$PowerUsageDataFromJson(json);
  Map<String, dynamic> toJson() => _$PowerUsageDataToJson(this);
}

@JsonSerializable()
class GeneratorUsage {
  final String generatorName;
  final double hoursRun;
  final double fuelConsumed;
  final double powerGenerated;
  final double efficiency;

  const GeneratorUsage({
    required this.generatorName,
    required this.hoursRun,
    required this.fuelConsumed,
    required this.powerGenerated,
    required this.efficiency,
  });

  factory GeneratorUsage.fromJson(Map<String, dynamic> json) => _$GeneratorUsageFromJson(json);
  Map<String, dynamic> toJson() => _$GeneratorUsageToJson(this);
}

@JsonSerializable()
class ElectricityMaintenance {
  final int overdueTasks;
  final int dueSoonTasks;
  final int completedTasks;
  final List<ElectricityMaintenanceTask> upcomingMaintenance;
  final List<ElectricityMaintenanceRecord> maintenanceHistory;

  const ElectricityMaintenance({
    required this.overdueTasks,
    required this.dueSoonTasks,
    required this.completedTasks,
    required this.upcomingMaintenance,
    required this.maintenanceHistory,
  });

  factory ElectricityMaintenance.fromJson(Map<String, dynamic> json) => _$ElectricityMaintenanceFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricityMaintenanceToJson(this);

  factory ElectricityMaintenance.empty() {
    return const ElectricityMaintenance(
      overdueTasks: 0,
      dueSoonTasks: 0,
      completedTasks: 0,
      upcomingMaintenance: [],
      maintenanceHistory: [],
    );
  }
}

@JsonSerializable()
class ElectricityMaintenanceTask {
  final String id;
  final String title;
  final String description;
  final String type;
  final DateTime dueDate;
  final String priority;
  final String status;
  final String? assignedTo;

  const ElectricityMaintenanceTask({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.dueDate,
    required this.priority,
    required this.status,
    this.assignedTo,
  });

  factory ElectricityMaintenanceTask.fromJson(Map<String, dynamic> json) => _$ElectricityMaintenanceTaskFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricityMaintenanceTaskToJson(this);
}

@JsonSerializable()
class ElectricityMaintenanceRecord {
  final String id;
  final String title;
  final String type;
  final DateTime completedDate;
  final double? cost;
  final String technician;
  final String notes;

  const ElectricityMaintenanceRecord({
    required this.id,
    required this.title,
    required this.type,
    required this.completedDate,
    this.cost,
    required this.technician,
    required this.notes,
  });

  factory ElectricityMaintenanceRecord.fromJson(Map<String, dynamic> json) => _$ElectricityMaintenanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$ElectricityMaintenanceRecordToJson(this);
}

@JsonSerializable()
class LoadManagement {
  final double currentLoad;
  final double maxCapacity;
  final double loadPercentage;
  final List<LoadZone> loadZones;
  final List<LoadSchedule> schedules;
  final List<LoadAlert> alerts;

  const LoadManagement({
    required this.currentLoad,
    required this.maxCapacity,
    required this.loadPercentage,
    required this.loadZones,
    required this.schedules,
    required this.alerts,
  });

  factory LoadManagement.fromJson(Map<String, dynamic> json) => _$LoadManagementFromJson(json);
  Map<String, dynamic> toJson() => _$LoadManagementToJson(this);

  factory LoadManagement.empty() {
    return const LoadManagement(
      currentLoad: 0,
      maxCapacity: 0,
      loadPercentage: 0,
      loadZones: [],
      schedules: [],
      alerts: [],
    );
  }
}

@JsonSerializable()
class LoadZone {
  final String id;
  final String name;
  final double currentLoad;
  final double maxLoad;
  final String status;
  final bool isActive;

  const LoadZone({
    required this.id,
    required this.name,
    required this.currentLoad,
    required this.maxLoad,
    required this.status,
    required this.isActive,
  });

  factory LoadZone.fromJson(Map<String, dynamic> json) => _$LoadZoneFromJson(json);
  Map<String, dynamic> toJson() => _$LoadZoneToJson(this);
}

@JsonSerializable()
class LoadSchedule {
  final String id;
  final String name;
  final String description;
  final DateTime startTime;
  final DateTime endTime;
  final List<String> affectedZones;
  final bool isActive;

  const LoadSchedule({
    required this.id,
    required this.name,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.affectedZones,
    required this.isActive,
  });

  factory LoadSchedule.fromJson(Map<String, dynamic> json) => _$LoadScheduleFromJson(json);
  Map<String, dynamic> toJson() => _$LoadScheduleToJson(this);
}

@JsonSerializable()
class LoadAlert {
  final String id;
  final String message;
  final String severity;
  final DateTime timestamp;
  final String? affectedZone;

  const LoadAlert({
    required this.id,
    required this.message,
    required this.severity,
    required this.timestamp,
    this.affectedZone,
  });

  factory LoadAlert.fromJson(Map<String, dynamic> json) => _$LoadAlertFromJson(json);
  Map<String, dynamic> toJson() => _$LoadAlertToJson(this);
}
