"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const employeeConfigController_1 = require("../controllers/employeeConfigController");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(auth_1.authenticateToken);
// Department routes
router.get('/departments', employeeConfigController_1.getDepartments);
router.get('/departments/:id', employeeConfigController_1.getDepartmentById);
router.post('/departments', employeeConfigController_1.createDepartment);
router.put('/departments/:id', employeeConfigController_1.updateDepartment);
router.delete('/departments/:id', employeeConfigController_1.deleteDepartment);
// Employee status routes
router.get('/statuses', employeeConfigController_1.getEmployeeStatuses);
router.post('/statuses', employeeConfigController_1.createEmployeeStatus);
router.put('/statuses/:id', employeeConfigController_1.updateEmployeeStatus);
router.delete('/statuses/:id', employeeConfigController_1.deleteEmployeeStatus);
exports.default = router;
