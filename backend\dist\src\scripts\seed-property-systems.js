"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedPropertySystems() {
    console.log('🌱 Seeding property system configurations...');
    try {
        // Get all properties
        const properties = await prisma.property.findMany();
        if (properties.length === 0) {
            console.log('No properties found. Please seed properties first.');
            return;
        }
        // Default system configurations for each property
        const defaultSystems = [
            {
                systemType: client_1.SystemType.WATER,
                displayName: 'Water Management',
                displayOrder: 1,
                isEnabled: true,
                configuration: {
                    features: ['tank_monitoring', 'quality_testing', 'maintenance_tracking'],
                    alertThresholds: {
                        lowLevel: 20,
                        criticalLevel: 10,
                    },
                },
            },
            {
                systemType: client_1.SystemType.ELECTRICITY,
                displayName: 'Electricity Management',
                displayOrder: 2,
                isEnabled: true,
                configuration: {
                    features: ['power_monitoring', 'generator_status', 'consumption_tracking'],
                    alertThresholds: {
                        highConsumption: 80,
                        generatorFuelLow: 25,
                    },
                },
            },
            {
                systemType: client_1.SystemType.SECURITY,
                displayName: 'Security Management',
                displayOrder: 3,
                isEnabled: true,
                configuration: {
                    features: ['camera_monitoring', 'access_control', 'incident_tracking'],
                    alertThresholds: {
                        cameraOffline: 1,
                        unauthorizedAccess: 0,
                    },
                },
            },
            {
                systemType: client_1.SystemType.INTERNET,
                displayName: 'Internet Management',
                displayOrder: 4,
                isEnabled: true,
                configuration: {
                    features: ['connectivity_monitoring', 'speed_testing', 'usage_tracking'],
                    alertThresholds: {
                        lowSpeed: 50,
                        highUsage: 90,
                    },
                },
            },
            {
                systemType: client_1.SystemType.MAINTENANCE,
                displayName: 'Maintenance Management',
                displayOrder: 5,
                isEnabled: true,
                configuration: {
                    features: ['task_scheduling', 'issue_tracking', 'preventive_maintenance'],
                    alertThresholds: {
                        overdueTasks: 3,
                        criticalIssues: 1,
                    },
                },
            },
        ];
        // Create system configurations for each property
        for (const property of properties) {
            console.log(`Creating system configs for property: ${property.name}`);
            for (const systemConfig of defaultSystems) {
                // Check if config already exists
                const existingConfig = await prisma.propertySystemConfig.findUnique({
                    where: {
                        propertyId_systemType: {
                            propertyId: property.id,
                            systemType: systemConfig.systemType,
                        },
                    },
                });
                if (!existingConfig) {
                    await prisma.propertySystemConfig.create({
                        data: {
                            propertyId: property.id,
                            ...systemConfig,
                        },
                    });
                    console.log(`  ✅ Created ${systemConfig.systemType} config`);
                }
                else {
                    console.log(`  ⏭️  ${systemConfig.systemType} config already exists`);
                }
            }
        }
        // Create sample system contacts
        console.log('Creating sample system contacts...');
        const sampleContacts = [
            {
                systemType: client_1.SystemType.WATER,
                name: 'Municipal Water Board',
                organization: 'City Water Authority',
                phone: '1916',
                email: '<EMAIL>',
                contactType: 'municipal',
                displayOrder: 1,
            },
            {
                systemType: client_1.SystemType.WATER,
                name: 'Emergency Plumber',
                organization: '24/7 Plumbing Services',
                phone: '+91 98765 12345',
                email: '<EMAIL>',
                contactType: 'emergency',
                displayOrder: 2,
            },
            {
                systemType: client_1.SystemType.ELECTRICITY,
                name: 'Electricity Board',
                organization: 'State Electricity Board',
                phone: '1912',
                email: '<EMAIL>',
                contactType: 'municipal',
                displayOrder: 1,
            },
            {
                systemType: client_1.SystemType.ELECTRICITY,
                name: 'Generator Service',
                organization: 'Power Solutions Ltd',
                phone: '+91 98765 54321',
                email: '<EMAIL>',
                contactType: 'maintenance',
                displayOrder: 2,
            },
            {
                systemType: client_1.SystemType.SECURITY,
                name: 'Local Police Station',
                organization: 'City Police',
                phone: '100',
                email: '<EMAIL>',
                contactType: 'emergency',
                displayOrder: 1,
            },
            {
                systemType: client_1.SystemType.SECURITY,
                name: 'Security Agency',
                organization: 'SecureGuard Services',
                phone: '+91 98765 67890',
                email: '<EMAIL>',
                contactType: 'maintenance',
                displayOrder: 2,
            },
        ];
        for (const property of properties) {
            for (const contact of sampleContacts) {
                // Check if contact already exists
                const existingContact = await prisma.systemContact.findFirst({
                    where: {
                        propertyId: property.id,
                        systemType: contact.systemType,
                        name: contact.name,
                    },
                });
                if (!existingContact) {
                    await prisma.systemContact.create({
                        data: {
                            propertyId: property.id,
                            ...contact,
                        },
                    });
                    console.log(`  ✅ Created ${contact.systemType} contact: ${contact.name}`);
                }
            }
        }
        // Create sample system content
        console.log('Creating sample system content...');
        const sampleContent = [
            {
                systemType: client_1.SystemType.WATER,
                contentType: 'maintenance_task',
                title: 'Monthly Tank Cleaning',
                content: {
                    description: 'Clean and disinfect water tanks',
                    frequency: 'Monthly',
                    duration: '2-3 hours',
                    requirements: ['Cleaning supplies', 'Safety equipment'],
                },
                displayOrder: 1,
            },
            {
                systemType: client_1.SystemType.WATER,
                contentType: 'quality_parameter',
                title: 'pH Level Monitoring',
                content: {
                    parameter: 'pH',
                    normalRange: '6.5 - 8.5',
                    testFrequency: 'Weekly',
                    alertThreshold: 'Outside normal range',
                },
                displayOrder: 2,
            },
            {
                systemType: client_1.SystemType.ELECTRICITY,
                contentType: 'maintenance_task',
                title: 'Generator Maintenance',
                content: {
                    description: 'Check generator oil, filters, and battery',
                    frequency: 'Monthly',
                    duration: '1-2 hours',
                    requirements: ['Engine oil', 'Air filter', 'Fuel filter'],
                },
                displayOrder: 1,
            },
        ];
        for (const property of properties) {
            for (const content of sampleContent) {
                // Check if content already exists
                const existingContent = await prisma.systemContent.findFirst({
                    where: {
                        propertyId: property.id,
                        systemType: content.systemType,
                        contentType: content.contentType,
                        title: content.title,
                    },
                });
                if (!existingContent) {
                    await prisma.systemContent.create({
                        data: {
                            propertyId: property.id,
                            ...content,
                        },
                    });
                    console.log(`  ✅ Created ${content.systemType} content: ${content.title}`);
                }
            }
        }
        console.log('✅ Property system configurations seeded successfully!');
    }
    catch (error) {
        console.error('❌ Error seeding property systems:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the seed function
if (require.main === module) {
    seedPropertySystems()
        .then(() => {
        console.log('🎉 Seeding completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Seeding failed:', error);
        process.exit(1);
    });
}
exports.default = seedPropertySystems;
