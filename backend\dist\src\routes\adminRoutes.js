"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const adminController_1 = require("../controllers/adminController");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(auth_1.authenticateToken);
// Role management routes
router.get('/roles', adminController_1.getRoles);
router.get('/roles/:id', adminController_1.getRoleById);
router.post('/roles', adminController_1.createRole);
router.put('/roles/:id', adminController_1.updateRole);
router.delete('/roles/:id', adminController_1.deleteRole);
// Permission management routes
router.get('/permissions', adminController_1.getPermissions);
// User role assignment routes
router.post('/user-roles', adminController_1.assignUserRole);
router.delete('/user-roles/:userId/:roleId', adminController_1.removeUserRole);
exports.default = router;
