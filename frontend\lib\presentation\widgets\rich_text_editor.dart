import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:html/parser.dart' as html_parser;

enum ContentFormat { markdown, html }

class RichTextEditor extends StatefulWidget {
  final String? initialContent;
  final ContentFormat format;
  final Function(String content) onContentChanged;
  final bool readOnly;
  final String? placeholder;

  const RichTextEditor({
    super.key,
    this.initialContent,
    this.format = ContentFormat.markdown,
    required this.onContentChanged,
    this.readOnly = false,
    this.placeholder,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late TextEditingController _markdownController;
  late TextEditingController _htmlController;

  bool _isPreviewMode = false;
  ContentFormat _currentFormat = ContentFormat.markdown;

  @override
  void initState() {
    super.initState();
    _currentFormat = widget.format;

    // Initialize controllers
    _markdownController = TextEditingController(text: widget.initialContent ?? '');
    _htmlController = TextEditingController(text: widget.initialContent ?? '');

    // Set up listeners
    _markdownController.addListener(_onMarkdownChanged);
    _htmlController.addListener(_onHtmlChanged);

    // Load initial content
    if (widget.initialContent != null && widget.initialContent!.isNotEmpty) {
      _loadInitialContent();
    }
  }

  @override
  void dispose() {
    _markdownController.dispose();
    _htmlController.dispose();
    super.dispose();
  }

  void _loadInitialContent() {
    if (_currentFormat == ContentFormat.markdown) {
      _markdownController.text = widget.initialContent!;
    } else {
      _htmlController.text = widget.initialContent!;
    }
  }

  void _onMarkdownChanged() {
    if (_currentFormat == ContentFormat.markdown) {
      widget.onContentChanged(_markdownController.text);
    }
  }

  void _onHtmlChanged() {
    if (_currentFormat == ContentFormat.html) {
      widget.onContentChanged(_htmlController.text);
    }
  }

  String _htmlToMarkdown(String html) {
    // Basic HTML to Markdown conversion
    // This is a simplified version - you might want to use a proper converter
    return html
        .replaceAll(RegExp(r'<h1[^>]*>'), '# ')
        .replaceAll(RegExp(r'</h1>'), '\n\n')
        .replaceAll(RegExp(r'<h2[^>]*>'), '## ')
        .replaceAll(RegExp(r'</h2>'), '\n\n')
        .replaceAll(RegExp(r'<h3[^>]*>'), '### ')
        .replaceAll(RegExp(r'</h3>'), '\n\n')
        .replaceAll(RegExp(r'<strong[^>]*>'), '**')
        .replaceAll(RegExp(r'</strong>'), '**')
        .replaceAll(RegExp(r'<em[^>]*>'), '*')
        .replaceAll(RegExp(r'</em>'), '*')
        .replaceAll(RegExp(r'<p[^>]*>'), '')
        .replaceAll(RegExp(r'</p>'), '\n\n')
        .replaceAll(RegExp(r'<br[^>]*>'), '\n')
        .replaceAll(RegExp(r'<[^>]*>'), ''); // Remove remaining HTML tags
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        Expanded(
          child: _isPreviewMode ? _buildPreview() : _buildEditor(),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Format selector
          DropdownButton<ContentFormat>(
            value: _currentFormat,
            onChanged: widget.readOnly ? null : (format) {
              setState(() {
                _currentFormat = format!;
              });
            },
            items: const [
              DropdownMenuItem(
                value: ContentFormat.markdown,
                child: Text('Markdown'),
              ),
              DropdownMenuItem(
                value: ContentFormat.html,
                child: Text('HTML'),
              ),
            ],
          ),
          const SizedBox(width: 16),
          
          // Editor/Preview toggle
          ToggleButtons(
            isSelected: [!_isPreviewMode, _isPreviewMode],
            onPressed: (index) {
              setState(() {
                _isPreviewMode = index == 1;
              });
            },
            children: const [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Text('Edit'),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Text('Preview'),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Format help button
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showFormatHelp(),
            tooltip: 'Format Help',
          ),
        ],
      ),
    );
  }

  Widget _buildEditor() {
    if (_currentFormat == ContentFormat.markdown) {
      return _buildMarkdownEditor();
    } else {
      return _buildHtmlEditor();
    }
  }

  Widget _buildMarkdownEditor() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _markdownController,
        readOnly: widget.readOnly,
        maxLines: null,
        expands: true,
        decoration: InputDecoration(
          hintText: widget.placeholder ?? 'Enter Markdown content...',
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.all(16),
        ),
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildHtmlEditor() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _htmlController,
        readOnly: widget.readOnly,
        maxLines: null,
        expands: true,
        decoration: InputDecoration(
          hintText: widget.placeholder ?? 'Enter HTML content...',
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.all(16),
        ),
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildPreview() {
    final content = _currentFormat == ContentFormat.markdown
        ? _markdownController.text
        : _htmlController.text;

    if (content.isEmpty) {
      return const Center(
        child: Text(
          'No content to preview',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: _currentFormat == ContentFormat.markdown
            ? MarkdownBody(data: content)
            : _buildHtmlPreview(content),
      ),
    );
  }

  Widget _buildHtmlPreview(String htmlContent) {
    // Simple HTML preview - you can enhance this with a proper HTML renderer
    final document = html_parser.parse(htmlContent);
    final text = document.body?.text ?? htmlContent;

    return Text(
      text,
      style: const TextStyle(fontSize: 14),
    );
  }

  void _showFormatHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${_currentFormat == ContentFormat.markdown ? 'Markdown' : 'HTML'} Help'),
        content: SingleChildScrollView(
          child: _currentFormat == ContentFormat.markdown
              ? _buildMarkdownHelp()
              : _buildHtmlHelp(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildMarkdownHelp() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('# Heading 1', style: TextStyle(fontFamily: 'monospace')),
        Text('## Heading 2', style: TextStyle(fontFamily: 'monospace')),
        Text('### Heading 3', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('**Bold text**', style: TextStyle(fontFamily: 'monospace')),
        Text('*Italic text*', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('- List item 1', style: TextStyle(fontFamily: 'monospace')),
        Text('- List item 2', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('[Link text](URL)', style: TextStyle(fontFamily: 'monospace')),
        Text('![Image](URL)', style: TextStyle(fontFamily: 'monospace')),
      ],
    );
  }

  Widget _buildHtmlHelp() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('<h1>Heading 1</h1>', style: TextStyle(fontFamily: 'monospace')),
        Text('<h2>Heading 2</h2>', style: TextStyle(fontFamily: 'monospace')),
        Text('<h3>Heading 3</h3>', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('<strong>Bold text</strong>', style: TextStyle(fontFamily: 'monospace')),
        Text('<em>Italic text</em>', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('<ul><li>List item</li></ul>', style: TextStyle(fontFamily: 'monospace')),
        Text('<ol><li>Numbered item</li></ol>', style: TextStyle(fontFamily: 'monospace')),
        SizedBox(height: 8),
        Text('<a href="URL">Link text</a>', style: TextStyle(fontFamily: 'monospace')),
        Text('<img src="URL" alt="Image">', style: TextStyle(fontFamily: 'monospace')),
      ],
    );
  }
}
