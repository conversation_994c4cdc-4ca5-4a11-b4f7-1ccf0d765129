import { Request, Response } from 'express';
export declare const getSecurityLogs: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getSecurityLogById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createSecurityLog: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateSecurityLog: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteSecurityLog: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
