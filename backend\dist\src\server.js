"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const http_1 = require("http");
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
// Import middleware
const errorHandler_1 = require("@/middleware/errorHandler");
const rateLimiter_1 = require("@/middleware/rateLimiter");
// Import routes
const auth_1 = __importDefault(require("@/routes/auth"));
const properties_1 = __importDefault(require("@/routes/properties"));
const dashboard_1 = __importDefault(require("@/routes/dashboard"));
const offices_1 = __importDefault(require("@/routes/offices"));
const users_1 = __importDefault(require("@/routes/users"));
const reports_1 = __importDefault(require("@/routes/reports"));
const notifications_1 = __importDefault(require("@/routes/notifications"));
const files_1 = __importDefault(require("@/routes/files"));
const maintenance_1 = __importDefault(require("@/routes/maintenance"));
const ott_1 = __importDefault(require("@/routes/ott"));
const systems_1 = __importDefault(require("@/routes/systems"));
const property_systems_1 = __importDefault(require("@/routes/property-systems"));
const alerts_1 = __importDefault(require("@/routes/alerts"));
const employees_1 = __importDefault(require("@/routes/employees"));
const rbac_1 = __importDefault(require("./routes/rbac"));
const adminRoutes_1 = __importDefault(require("./routes/adminRoutes"));
const analyticsRoutes_1 = __importDefault(require("./routes/analyticsRoutes"));
const securityLogsRoutes_1 = __importDefault(require("./routes/securityLogsRoutes"));
const constructionSitesRoutes_1 = __importDefault(require("./routes/constructionSitesRoutes"));
const employeeConfigRoutes_1 = __importDefault(require("./routes/employeeConfigRoutes"));
// Import services
const prisma_1 = require("@/lib/prisma");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
const API_VERSION = process.env.API_VERSION || 'v1';
// Create HTTP server
const server = (0, http_1.createServer)(app);
// Swagger configuration
const swaggerOptions = {
    definition: {
        openapi: '3.0.3',
        info: {
            title: 'SRSR Property Management API',
            version: '1.0.0',
            description: 'Comprehensive API for SRSR Property Management System with RBAC, real-time updates, and multi-property management capabilities.',
            contact: {
                name: 'SRSR Property Management',
                email: '<EMAIL>',
                url: 'https://srsrproperty.com',
            },
            license: {
                name: 'MIT',
                url: 'https://opensource.org/licenses/MIT',
            },
        },
        servers: [
            {
                url: `http://localhost:${PORT}/${API_VERSION}`,
                description: 'Development server',
            },
            {
                url: `https://api.srsrproperty.com/${API_VERSION}`,
                description: 'Production server',
            },
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                    description: 'JWT token for authentication. Include in Authorization header as \'Bearer {token}\'',
                },
            },
        },
        security: [
            {
                bearerAuth: [],
            },
        ],
    },
    apis: ['./src/routes/*.ts'], // Path to the API files
};
const swaggerSpec = (0, swagger_jsdoc_1.default)(swaggerOptions);
// Security middleware
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false,
}));
// CORS configuration
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin)
            return callback(null, true);
        // In development, allow all localhost and 127.0.0.1 origins
        if (process.env.NODE_ENV !== 'production') {
            if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
                return callback(null, true);
            }
        }
        // Check against allowed origins
        const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:8080',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://127.0.0.1:8080'
        ];
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            console.log(`CORS blocked origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};
app.use((0, cors_1.default)(corsOptions));
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Compression middleware
app.use((0, compression_1.default)());
// Logging middleware
if (process.env.NODE_ENV !== 'test') {
    app.use((0, morgan_1.default)(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
}
// Rate limiting
app.use(rateLimiter_1.endpointSpecificRateLimit);
// Health check endpoint (no auth required)
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
    });
});
// Detailed health check endpoint
app.get('/health/detailed', async (req, res) => {
    try {
        // Test database connection
        const dbStart = Date.now();
        await prisma_1.prisma.$queryRaw `SELECT 1`;
        const dbResponseTime = Date.now() - dbStart;
        // Get memory usage
        const memoryUsage = process.memoryUsage();
        const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            services: {
                database: {
                    status: 'healthy',
                    responseTime: dbResponseTime,
                    lastCheck: new Date().toISOString(),
                },
                storage: {
                    status: 'healthy',
                    responseTime: 0,
                    lastCheck: new Date().toISOString(),
                },
            },
            metrics: {
                memoryUsage: Math.round(memoryUsagePercent),
                cpuUsage: 0, // Would need additional monitoring for this
                activeConnections: 0, // Would need connection tracking
            },
        });
    }
    catch (error) {
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// API Documentation
if (process.env.SWAGGER_ENABLED === 'true') {
    app.use(process.env.SWAGGER_PATH || '/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'SRSR Property Management API',
    }));
}
// API routes
app.use(`/${API_VERSION}/auth`, auth_1.default);
app.use(`/${API_VERSION}/properties`, properties_1.default);
app.use(`/${API_VERSION}/dashboard`, dashboard_1.default);
app.use(`/${API_VERSION}/offices`, offices_1.default);
app.use(`/${API_VERSION}/users`, users_1.default);
app.use(`/${API_VERSION}/reports`, reports_1.default);
app.use(`/${API_VERSION}/notifications`, notifications_1.default);
app.use(`/${API_VERSION}/files`, files_1.default);
app.use(`/${API_VERSION}/maintenance`, maintenance_1.default);
app.use(`/${API_VERSION}/ott`, ott_1.default);
app.use(`/${API_VERSION}/systems`, systems_1.default);
app.use(`/${API_VERSION}/property-systems`, property_systems_1.default);
app.use(`/${API_VERSION}/alerts`, alerts_1.default);
app.use(`/${API_VERSION}/employees`, employees_1.default);
app.use(`/${API_VERSION}/rbac`, rbac_1.default);
app.use(`/${API_VERSION}/admin`, adminRoutes_1.default);
app.use(`/${API_VERSION}/analytics`, analyticsRoutes_1.default);
app.use(`/${API_VERSION}/security/logs`, securityLogsRoutes_1.default);
app.use(`/${API_VERSION}/construction-sites`, constructionSitesRoutes_1.default);
app.use(`/${API_VERSION}/employees`, employeeConfigRoutes_1.default);
// 404 handler
app.use(errorHandler_1.notFoundHandler);
// Global error handler
app.use(errorHandler_1.errorHandler);
// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
    });
    await prisma_1.prisma.$disconnect();
    console.log('Database connection closed');
    process.exit(0);
});
process.on('SIGINT', async () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
    });
    await prisma_1.prisma.$disconnect();
    console.log('Database connection closed');
    process.exit(0);
});
// Start server
if (process.env.NODE_ENV !== 'test') {
    server.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`📚 API Documentation: http://localhost:${PORT}${process.env.SWAGGER_PATH || '/api-docs'}`);
        console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    });
}
exports.default = app;
