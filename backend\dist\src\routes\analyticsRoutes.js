"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const analyticsController_1 = require("../controllers/analyticsController");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(auth_1.authenticateToken);
// Analytics routes
router.get('/maintenance', analyticsController_1.getMaintenanceAnalytics);
router.get('/security', analyticsController_1.getSecurityAnalytics);
exports.default = router;
