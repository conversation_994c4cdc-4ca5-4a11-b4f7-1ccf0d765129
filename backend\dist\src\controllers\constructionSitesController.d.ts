import { Request, Response } from 'express';
export declare const getConstructionSites: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getConstructionSiteById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createConstructionSite: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateConstructionSite: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteConstructionSite: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const recordAttendance: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAttendance: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
