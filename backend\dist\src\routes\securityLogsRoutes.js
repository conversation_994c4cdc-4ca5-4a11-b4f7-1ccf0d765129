"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const securityLogsController_1 = require("../controllers/securityLogsController");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(auth_1.authenticateToken);
// Security logs routes
router.get('/', securityLogsController_1.getSecurityLogs);
router.get('/:id', securityLogsController_1.getSecurityLogById);
router.post('/', securityLogsController_1.createSecurityLog);
router.put('/:id', securityLogsController_1.updateSecurityLog);
router.delete('/:id', securityLogsController_1.deleteSecurityLog);
exports.default = router;
